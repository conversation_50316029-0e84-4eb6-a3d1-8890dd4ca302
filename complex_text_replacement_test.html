<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>复杂文段替换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .test-section h3 {
            color: #0066cc;
            margin-top: 0;
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .test-case {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .test-case h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .original-text {
            background-color: #fff3cd;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
            border: 1px solid #ffeaa7;
        }
        .replacement-text {
            background-color: #d4edda;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
            border: 1px solid #c3e6cb;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background-color: white;
            margin-top: 20px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>复杂文段替换功能测试</h1>
        
        <div class="test-section">
            <h3>测试说明</h3>
            <p>此页面专门测试跨元素的复杂文段替换功能。PDF中的长文本可能被分割到多个DOM元素中，我们的算法需要能够识别和替换这些跨元素的文本。</p>
            <p><strong>测试原理</strong>：通过去除空格和标点符号标准化，在连续文本中查找匹配，然后计算涉及的所有元素的边界框进行替换。</p>
        </div>

        <div class="test-section">
            <h3>测试用例</h3>
            
            <div class="test-case">
                <h4>测试1：政府采购条例文段</h4>
                <div class="original-text">原文：深圳经济特区政府采购条例》第五十七条 供应商在政府采购中，有下列行为之一的，一至</div>
                <div class="replacement-text">替换：【政府采购违规行为条款】</div>
                <button class="btn-primary" onclick="testGovernmentProcurement()">测试政府采购条例</button>
            </div>

            <div class="test-case">
                <h4>测试2：长标题替换</h4>
                <div class="original-text">原文：TraceMonkey: Fast, Native Code Generation for Dynamic Languages</div>
                <div class="replacement-text">替换：TraceMonkey：动态语言的快速原生代码生成</div>
                <button class="btn-success" onclick="testLongTitle()">测试长标题</button>
            </div>

            <div class="test-case">
                <h4>测试3：作者列表替换</h4>
                <div class="original-text">原文：Andreas Gal, Brendan Eich, Mike Shaver, David Anderson...</div>
                <div class="replacement-text">替换：作者：Andreas Gal等人</div>
                <button class="btn-warning" onclick="testAuthorList()">测试作者列表</button>
            </div>

            <div class="test-case">
                <h4>测试4：技术术语替换</h4>
                <div class="original-text">原文：dynamic compilation, trace-based compilation</div>
                <div class="replacement-text">替换：动态编译，基于跟踪的编译</div>
                <button class="btn-primary" onclick="testTechnicalTerms()">测试技术术语</button>
            </div>

            <div class="test-case">
                <h4>测试5：混合测试</h4>
                <div class="original-text">同时测试多个复杂文段的替换</div>
                <button class="btn-danger" onclick="testMixedReplacements()">混合测试</button>
            </div>
        </div>

        <div class="test-section">
            <h3>控制面板</h3>
            <div class="button-group">
                <button class="btn-success" onclick="loadOriginalPDF()">加载原始PDF</button>
                <button class="btn-warning" onclick="clearAllReplacements()">清除所有替换</button>
                <button class="btn-primary" onclick="testCustomReplacement()">自定义测试</button>
            </div>
        </div>

        <div id="status" class="status info">
            准备就绪，请选择测试用例
        </div>

        <iframe id="pdfViewer" src="about:blank"></iframe>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function loadPDFWithReplacements(textReplacements, testName) {
            const encodedReplacements = encodeURIComponent(JSON.stringify(textReplacements));
            const url = `build/generic/web/viewer.html?file=[SZCG2025000306-A]深圳大学电化学测试仪采购(1).pdf&textReplacements=${encodedReplacements}`;
            
            document.getElementById('pdfViewer').src = url;
            updateStatus(`${testName}：已加载PDF，应用了 ${textReplacements.length} 个文本替换`, 'success');
        }

        function testGovernmentProcurement() {
            const replacements = [
                {
                    page: 1,
                    originalText: "深圳经济特区政府采购条例实施细则》第七十五条 供应商有下列情形之一的，属于采购条例所称的串通投标行为，按照采购条例第五十七条有关规定处理",
                    replacementText: "【政府采购违规行为条款】",
                    style: {
                        backgroundColor: "rgba(255, 0, 0, 0.2)",
                        color: "#000000",
                        border: "2px solid rgba(255, 0, 0, 0.8)",
                        fontWeight: "bold"
                    }
                }
            ];
            
            loadPDFWithReplacements(replacements, "政府采购条例测试");
        }

        function testLongTitle() {
            const replacements = [
                {
                    page: 1,
                    originalText: "TraceMonkey: Fast, Native Code Generation for Dynamic Languages",
                    replacementText: "TraceMonkey：动态语言的快速原生代码生成",
                    style: {
                        backgroundColor: "rgba(0, 123, 255, 0.2)",
                        color: "#000000",
                        border: "2px solid rgba(0, 123, 255, 0.8)",
                        fontWeight: "bold",
                        fontSize: "1.1em"
                    }
                }
            ];
            
            loadPDFWithReplacements(replacements, "长标题测试");
        }

        function testAuthorList() {
            const replacements = [
                {
                    page: 1,
                    originalText: "Andreas Gal, Brendan Eich, Mike Shaver, David Anderson, David Mandelin, Mohammad R. Haghighat, Blake Kaplan, Graydon Hoare, Boris Zbarsky, Jason Orendorff, Jesse Ruderman, Edwin W. Smith, Rick Reitmaier, Michael Bebenita, Mason Chang, Michael Franz",
                    replacementText: "作者：Andreas Gal等人",
                    style: {
                        backgroundColor: "rgba(255, 193, 7, 0.2)",
                        color: "#000000",
                        border: "1px solid rgba(255, 193, 7, 0.8)"
                    }
                }
            ];
            
            loadPDFWithReplacements(replacements, "作者列表测试");
        }

        function testTechnicalTerms() {
            const replacements = [
                {
                    page: 1,
                    originalText: "dynamic compilation",
                    replacementText: "动态编译",
                    style: {
                        backgroundColor: "rgba(40, 167, 69, 0.2)",
                        color: "#000000",
                        border: "1px solid rgba(40, 167, 69, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "trace-based compilation",
                    replacementText: "基于跟踪的编译",
                    style: {
                        backgroundColor: "rgba(108, 117, 125, 0.2)",
                        color: "#000000",
                        border: "1px solid rgba(108, 117, 125, 0.8)"
                    }
                }
            ];
            
            loadPDFWithReplacements(replacements, "技术术语测试");
        }

        function testMixedReplacements() {
            const replacements = [
                {
                    page: 1,
                    originalText: "TraceMonkey: Fast, Native Code Generation for Dynamic Languages",
                    replacementText: "TraceMonkey：动态语言的快速原生代码生成",
                    style: {
                        backgroundColor: "rgba(255, 0, 0, 0.2)",
                        color: "#000000",
                        border: "2px solid rgba(255, 0, 0, 0.8)",
                        fontWeight: "bold"
                    }
                },
                {
                    page: 1,
                    originalText: "Mozilla Corporation",
                    replacementText: "Mozilla公司",
                    style: {
                        backgroundColor: "rgba(0, 255, 0, 0.2)",
                        color: "#000000",
                        border: "1px solid rgba(0, 255, 0, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "University of California, Irvine",
                    replacementText: "加州大学尔湾分校",
                    style: {
                        backgroundColor: "rgba(0, 0, 255, 0.2)",
                        color: "#ffffff",
                        border: "1px solid rgba(0, 0, 255, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "dynamic compilation",
                    replacementText: "动态编译",
                    style: {
                        backgroundColor: "rgba(255, 255, 0, 0.2)",
                        color: "#000000",
                        border: "1px solid rgba(255, 255, 0, 0.8)"
                    }
                }
            ];
            
            loadPDFWithReplacements(replacements, "混合测试");
        }

        function loadOriginalPDF() {
            updateStatus('加载原始PDF（无文本替换）', 'info');
            
            const url = `build/generic/web/viewer.html?file=[SZCG2025000306-A]深圳大学电化学测试仪采购(1).pdf`;
            document.getElementById('pdfViewer').src = url;
            updateStatus('已加载原始PDF文档', 'success');
        }

        function clearAllReplacements() {
            updateStatus('清除所有文本替换', 'info');
            
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'clearTextReplacements'
                }, '*');
                
                updateStatus('已清除所有文本替换', 'success');
            } else {
                updateStatus('请先加载PDF文档', 'error');
            }
        }

        function testCustomReplacement() {
            const originalText = prompt('请输入要替换的原始文本：');
            if (!originalText) return;
            
            const replacementText = prompt('请输入替换后的文本：');
            if (!replacementText) return;
            
            const replacements = [
                {
                    page: 1,
                    originalText: originalText,
                    replacementText: replacementText,
                    style: {
                        backgroundColor: "rgba(255, 0, 255, 0.2)",
                        color: "#000000",
                        border: "1px solid rgba(255, 0, 255, 0.8)"
                    }
                }
            ];
            
            loadPDFWithReplacements(replacements, "自定义测试");
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'textReplacementStatus') {
                updateStatus(`PDF查看器状态：${event.data.message}`, event.data.success ? 'success' : 'error');
            }
        });

        // 页面加载完成后的初始化
        window.onload = function() {
            updateStatus('复杂文段替换测试页面已加载，请选择测试用例', 'info');
        };
    </script>
</body>
</html>
