<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <title>PDF with Full Features</title>
</head>
<body>
    <h1>PDF查看器（包含文本替换预览功能）</h1>

    <button onclick="loadPDFWithFullFeatures()">加载带文本替换的PDF</button>
    <button onclick="loadDemoReplacements()">加载演示文本替换</button>
    
    <iframe 
        id="pdfViewer" 
        width="100%" 
        height="600px" 
        style="border: 1px solid #ccc;"
        sandbox="allow-scripts allow-same-origin allow-forms allow-downloads">
    </iframe>

    <script>
        function loadPDFWithFullFeatures() {
            const pdfFile = 'compressed.tracemonkey-pldi-09.pdf';

            // 高亮配置
            const highlights = [{
                page: 1,
                x1: 100, y1: 200, x2: 400, y2: 250,
                colorClass: 'yellow'
            }];

            // 文本替换配置
            const textReplacements = [
                {
                    page: 1,
                    originalText: "JavaScript",
                    replacementText: "TypeScript",
                    style: {
                        backgroundColor: "rgba(255, 255, 0, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(255, 255, 0, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "Mozilla",
                    replacementText: "OpenAI",
                    style: {
                        backgroundColor: "rgba(0, 255, 0, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(0, 255, 0, 0.8)"
                    }
                }
            ];

            const encodedHighlights = encodeURIComponent(JSON.stringify(highlights));
            const encodedTextReplacements = encodeURIComponent(JSON.stringify(textReplacements));

            // 包含所有必要参数的URL
            const url = `build/generic/web/viewer.html?` +
                `file=${pdfFile}&` +
                `highlights=${encodedHighlights}&` +
                `textReplacements=${encodedTextReplacements}`

            document.getElementById('pdfViewer').src = url;
        }

        // 通过postMessage接收文本替换配置
        function receiveTextReplacements(event) {
            // 验证来源（在实际应用中应该验证origin）
            if (!event.data || typeof event.data !== 'object') {
                return;
            }

            const { type, data } = event.data;

            switch (type) {
                case 'textReplacements':
                case 'setTextReplacements':
                    if (data && Array.isArray(data.replacements)) {
                        const config = normalizeTextReplacementConfig(data);
                        loadPDFWithTextReplacements(
                            config.pdfFile || 'compressed.tracemonkey-pldi-09.pdf',
                            config.replacements,
                            config.globalStyle
                        );
                    }
                    break;
                case 'addTextReplacement':
                    if (data && data.replacement) {
                        addSingleTextReplacement(data.replacement);
                    }
                    break;
                case 'clearTextReplacements':
                    clearAllTextReplacements();
                    break;
                case 'undoTextReplacement':
                    undoTextReplacement();
                    break;
                case 'redoTextReplacement':
                    redoTextReplacement();
                    break;
                case 'getTextReplacementState':
                    sendTextReplacementState();
                    break;
            }
        }

        // 标准化文本替换配置
        function normalizeTextReplacementConfig(data) {
            // 支持旧格式兼容
            if (data.replacements && !data.pdfFile) {
                return {
                    replacements: data.replacements,
                    pdfFile: data.pdfFile,
                    globalStyle: data.globalStyle || {}
                };
            }

            // 标准化新格式
            return {
                replacements: data.replacements || [],
                pdfFile: data.pdfFile,
                globalStyle: data.globalStyle || {
                    highlightColor: 'rgba(255, 255, 0, 0.3)',
                    opacity: 0.3
                }
            };
        }

        // 使用指定的文本替换配置加载PDF
        function loadPDFWithTextReplacements(pdfFile, textReplacements, globalStyle = {}) {
            const encodedTextReplacements = encodeURIComponent(JSON.stringify(textReplacements));

            let url = `build/generic/web/viewer.html?` +
                `file=${pdfFile}&` +
                `textReplacements=${encodedTextReplacements}`;

            // Add global style if provided
            if (globalStyle && Object.keys(globalStyle).length > 0) {
                const encodedGlobalStyle = encodeURIComponent(JSON.stringify(globalStyle));
                url += `&globalStyle=${encodedGlobalStyle}`;
            }

            document.getElementById('pdfViewer').src = url;

            // Send status update
            sendStatusUpdate('PDF loaded with text replacements', true, {
                pdfFile,
                replacementCount: textReplacements.length,
                globalStyle
            });
        }

        // 添加单个文本替换
        function addSingleTextReplacement(replacement) {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'addTextReplacement',
                    data: { replacement }
                }, '*');

                sendStatusUpdate('Text replacement added', true, { replacement });
            }
        }

        // 清除所有文本替换
        function clearAllTextReplacements() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'clearTextReplacements'
                }, '*');

                sendStatusUpdate('All text replacements cleared', true);
            }
        }

        // 撤销文本替换
        function undoTextReplacement() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'undoTextReplacement'
                }, '*');

                sendStatusUpdate('Text replacement undone', true);
            }
        }

        // 重做文本替换
        function redoTextReplacement() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'redoTextReplacement'
                }, '*');

                sendStatusUpdate('Text replacement redone', true);
            }
        }

        // 获取文本替换状态
        function sendTextReplacementState() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'getTextReplacementState'
                }, '*');
            }
        }

        // 发送状态更新
        function sendStatusUpdate(message, success, data = {}) {
            // Send to parent window if in iframe
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'textReplacementStatus',
                    success,
                    message,
                    data,
                    timestamp: Date.now()
                }, '*');
            }

            // Log for debugging
            console.log(`TextReplacement Status: ${message}`, { success, data });
        }

        // 从URL参数获取文本替换配置
        function loadFromURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const textReplacementsParam = urlParams.get('textReplacements');
            const pdfFileParam = urlParams.get('file');

            if (textReplacementsParam) {
                try {
                    const textReplacements = JSON.parse(decodeURIComponent(textReplacementsParam));
                    loadPDFWithTextReplacements(
                        pdfFileParam || 'compressed.tracemonkey-pldi-09.pdf',
                        textReplacements
                    );
                    return true;
                } catch (error) {
                    console.error('Error parsing text replacements from URL:', error);
                }
            }
            return false;
        }

        // 加载演示文本替换
        function loadDemoReplacements() {
            const demoReplacements = [
                {
                    page: 1,
                    originalText: "trace",
                    replacementText: "追踪",
                    style: {
                        backgroundColor: "rgba(255, 192, 203, 0.3)",
                        color: "#000000",
                        border: "1px solid rgba(255, 192, 203, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "monkey",
                    replacementText: "猴子",
                    style: {
                        backgroundColor: "rgba(0, 150, 255, 0.3)",
                        color: "#ffffff",
                        border: "1px solid rgba(0, 150, 255, 0.8)"
                    }
                },
                {
                    page: 1,
                    originalText: "compilation",
                    replacementText: "编译",
                    style: {
                        backgroundColor: "rgba(128, 0, 128, 0.3)",
                        color: "#ffffff",
                        border: "1px solid rgba(128, 0, 128, 0.8)"
                    }
                }
            ];

            loadPDFWithTextReplacements('compressed.tracemonkey-pldi-09.pdf', demoReplacements);
        }

        // 页面加载时的初始化
        window.onload = function() {
            // 监听postMessage事件
            window.addEventListener('message', receiveTextReplacements, false);

            // 首先尝试从URL参数加载，如果没有则使用默认配置
            if (!loadFromURLParams()) {
                loadPDFWithFullFeatures();
            }
        };

        // 提供给外部调用的API
        window.PDFTextReplacementAPI = {
            // 设置文本替换（标准化接口）
            setTextReplacements: function(config) {
                if (typeof config === 'object' && config.replacements) {
                    const normalizedConfig = normalizeTextReplacementConfig(config);
                    loadPDFWithTextReplacements(
                        normalizedConfig.pdfFile || 'compressed.tracemonkey-pldi-09.pdf',
                        normalizedConfig.replacements,
                        normalizedConfig.globalStyle
                    );
                } else {
                    // 兼容旧接口
                    const pdfFile = arguments[0];
                    const textReplacements = arguments[1];
                    const globalStyle = arguments[2] || {};
                    loadPDFWithTextReplacements(pdfFile, textReplacements, globalStyle);
                }
            },

            // 添加单个文本替换
            addTextReplacement: function(replacement) {
                addSingleTextReplacement(replacement);
            },

            // 清除所有文本替换
            clearTextReplacements: function() {
                clearAllTextReplacements();
            },

            // 撤销操作
            undo: function() {
                undoTextReplacement();
            },

            // 重做操作
            redo: function() {
                redoTextReplacement();
            },

            // 获取当前状态
            getState: function() {
                sendTextReplacementState();
            },

            // 批量操作开始
            startBatch: function() {
                const iframe = document.getElementById('pdfViewer');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage({
                        type: 'startBatch'
                    }, '*');
                }
            },

            // 批量操作结束
            endBatch: function() {
                const iframe = document.getElementById('pdfViewer');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage({
                        type: 'endBatch'
                    }, '*');
                }
            },

            // 设置事件监听器
            addEventListener: function(eventType, callback) {
                window.addEventListener('message', function(event) {
                    if (event.data && event.data.type === 'textReplacementStatus') {
                        if (eventType === 'all' || event.data.message === eventType) {
                            callback(event.data);
                        }
                    }
                });
            }
        };
    </script>
</body>
</html>