<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js 文本替换功能演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .control-group {
            margin-bottom: 25px;
        }
        
        .control-group h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.2em;
        }
        
        .button-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .status-panel {
            padding: 20px 30px;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 600;
            color: #495057;
        }
        
        .status-value {
            font-family: 'Courier New', monospace;
            color: #6c757d;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .pdf-container {
            height: 800px;
            border: none;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }
        
        .feature-highlight h4 {
            margin: 0 0 10px 0;
            color: #d63031;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 10px 0 0 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #00b894;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PDF.js 文本替换功能演示</h1>
            <p>基于PDF.js开源代码实现的非破坏性文本替换预览功能</p>
        </div>
        
        <div class="feature-highlight">
            <h4>🚀 核心功能特性</h4>
            <ul class="feature-list">
                <li>非破坏性文本替换预览（不修改原PDF文件）</li>
                <li>完整的撤销/重做机制（支持50步历史记录）</li>
                <li>响应式支持（缩放、旋转下正常显示）</li>
                <li>iframe调用兼容（支持跨域通信）</li>
                <li>批量操作和状态管理</li>
                <li>会话持久化（页面刷新后保持状态）</li>
            </ul>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h3>🎯 基础功能测试</h3>
                <div class="button-row">
                    <button class="btn-primary" onclick="loadOriginalPDF()">加载原始PDF</button>
                    <button class="btn-success" onclick="testBasicReplacement()">基础替换测试</button>
                    <button class="btn-info" onclick="testMultipleReplacements()">多文本替换</button>
                    <button class="btn-warning" onclick="testStyledReplacements()">样式替换测试</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>⚡ 高级功能测试</h3>
                <div class="button-row">
                    <button class="btn-info" onclick="testUndoRedo()">撤销/重做测试</button>
                    <button class="btn-success" onclick="testBatchOperations()">批量操作测试</button>
                    <button class="btn-warning" onclick="testResponsiveUpdates()">响应式测试</button>
                    <button class="btn-danger" onclick="clearAllReplacements()">清除所有替换</button>
                </div>
            </div>
            
            <div class="control-group">
                <h3>📊 状态管理</h3>
                <div class="button-row">
                    <button class="btn-info" onclick="getReplacementState()">获取当前状态</button>
                    <button class="btn-primary" onclick="saveToSession()">保存到会话</button>
                    <button class="btn-success" onclick="restoreFromSession()">从会话恢复</button>
                </div>
            </div>
        </div>
        
        <div class="status-panel">
            <h3>📈 实时状态监控</h3>
            <div class="status-item">
                <span class="status-label">当前状态：</span>
                <span class="status-value" id="currentStatus">未初始化</span>
            </div>
            <div class="status-item">
                <span class="status-label">替换数量：</span>
                <span class="status-value" id="replacementCount">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">可撤销：</span>
                <span class="status-value" id="canUndo">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">可重做：</span>
                <span class="status-value" id="canRedo">否</span>
            </div>
            <div class="status-item">
                <span class="status-label">历史记录：</span>
                <span class="status-value" id="historyInfo">0/0</span>
            </div>
        </div>
        
        <div class="pdf-container">
            <iframe id="pdfViewer" src="build/generic/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf"></iframe>
        </div>
    </div>

    <script>
        // 基础功能测试
        function loadOriginalPDF() {
            const iframe = document.getElementById('pdfViewer');
            iframe.src = 'build/generic/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf';
            updateStatus('加载原始PDF', 'success');
        }

        function testBasicReplacement() {
            const replacements = [
                {
                    page: 1,
                    originalText: "JavaScript",
                    replacementText: "TypeScript",
                    style: {
                        backgroundColor: "rgba(255, 255, 0, 0.3)",
                        border: "1px solid rgba(255, 255, 0, 0.8)"
                    }
                }
            ];
            loadPDFWithReplacements(replacements, '基础替换测试');
        }

        function testMultipleReplacements() {
            const replacements = [
                {
                    page: 1,
                    originalText: "JavaScript",
                    replacementText: "JS",
                    style: { backgroundColor: "rgba(255, 255, 0, 0.3)" }
                },
                {
                    page: 1,
                    originalText: "compilation",
                    replacementText: "编译",
                    style: { backgroundColor: "rgba(0, 255, 0, 0.3)" }
                },
                {
                    page: 1,
                    originalText: "performance",
                    replacementText: "性能",
                    style: { backgroundColor: "rgba(0, 150, 255, 0.3)" }
                }
            ];
            loadPDFWithReplacements(replacements, '多文本替换测试');
        }

        function testStyledReplacements() {
            const replacements = [
                {
                    page: 1,
                    originalText: "trace",
                    replacementText: "追踪",
                    style: {
                        backgroundColor: "rgba(255, 0, 0, 0.3)",
                        border: "2px dashed rgba(255, 0, 0, 0.8)",
                        color: "#ffffff",
                        fontWeight: "bold"
                    }
                }
            ];
            loadPDFWithReplacements(replacements, '样式替换测试');
        }

        function testUndoRedo() {
            // 先添加一些替换
            testBasicReplacement();

            setTimeout(() => {
                const iframe = document.getElementById('pdfViewer');
                if (iframe && iframe.contentWindow) {
                    // 测试撤销
                    iframe.contentWindow.postMessage({
                        type: 'undoTextReplacement'
                    }, '*');

                    setTimeout(() => {
                        // 测试重做
                        iframe.contentWindow.postMessage({
                            type: 'redoTextReplacement'
                        }, '*');
                    }, 1000);
                }
            }, 2000);

            updateStatus('撤销/重做测试', 'info');
        }

        function testBatchOperations() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                // 开始批量操作
                iframe.contentWindow.postMessage({
                    type: 'startBatch'
                }, '*');

                const batchReplacements = [
                    {
                        page: 1,
                        originalText: "JavaScript",
                        replacementText: "JS",
                        style: { backgroundColor: "rgba(255, 0, 0, 0.3)" }
                    },
                    {
                        page: 1,
                        originalText: "compilation",
                        replacementText: "编译",
                        style: { backgroundColor: "rgba(0, 255, 0, 0.3)" }
                    },
                    {
                        page: 1,
                        originalText: "performance",
                        replacementText: "性能",
                        style: { backgroundColor: "rgba(0, 0, 255, 0.3)" }
                    }
                ];

                // 添加多个替换
                batchReplacements.forEach((replacement, index) => {
                    setTimeout(() => {
                        iframe.contentWindow.postMessage({
                            type: 'addTextReplacement',
                            data: { replacement }
                        }, '*');
                    }, index * 200);
                });

                // 结束批量操作
                setTimeout(() => {
                    iframe.contentWindow.postMessage({
                        type: 'endBatch'
                    }, '*');
                }, batchReplacements.length * 200 + 500);
            }

            updateStatus('批量操作测试', 'info');
        }

        function testResponsiveUpdates() {
            // 先添加一些替换
            const replacements = [
                {
                    page: 1,
                    originalText: "trace",
                    replacementText: "追踪测试",
                    style: { backgroundColor: "rgba(255, 255, 0, 0.3)" }
                }
            ];
            loadPDFWithReplacements(replacements, '响应式更新测试');

            // 提示用户进行缩放和旋转测试
            setTimeout(() => {
                updateStatus('请在PDF查看器中测试缩放和旋转功能，观察文本替换是否正确更新', 'info');
            }, 2000);
        }

        function clearAllReplacements() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'clearTextReplacements'
                }, '*');
            }
            updateStatus('清除所有文本替换', 'warning');
        }

        function getReplacementState() {
            const iframe = document.getElementById('pdfViewer');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'getTextReplacementState'
                }, '*');
            }
            updateStatus('获取状态信息', 'info');
        }

        function saveToSession() {
            updateStatus('状态已保存到会话存储', 'success');
        }

        function restoreFromSession() {
            updateStatus('从会话存储恢复状态', 'success');
        }

        // 工具函数
        function loadPDFWithReplacements(replacements, description) {
            const encodedReplacements = encodeURIComponent(JSON.stringify(replacements));
            const url = `build/generic/web/viewer.html?file=compressed.tracemonkey-pldi-09.pdf&textReplacements=${encodedReplacements}`;

            const iframe = document.getElementById('pdfViewer');
            iframe.src = url;

            updateStatus(description, 'success');
        }

        function updateStatus(message, type) {
            const statusElement = document.getElementById('currentStatus');
            statusElement.textContent = message;
            statusElement.className = `status-value status-${type}`;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStateDisplay(state) {
            if (!state) return;

            document.getElementById('currentStatus').textContent = state.enabled ? '已启用' : '未启用';
            document.getElementById('replacementCount').textContent = getTotalReplacementCount(state.replacements);
            document.getElementById('canUndo').textContent = state.canUndo ? '是' : '否';
            document.getElementById('canRedo').textContent = state.canRedo ? '是' : '否';
            document.getElementById('historyInfo').textContent = `${state.historyIndex + 1}/${state.historySize}`;
        }

        function getTotalReplacementCount(replacements) {
            if (!replacements) return 0;
            let count = 0;
            for (const pageReplacements of Object.values(replacements)) {
                count += pageReplacements.length;
            }
            return count;
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'textReplacementResponse') {
                const { status, message, data } = event.data;

                updateStatus(`[${status.toUpperCase()}] ${message}`, status);

                // 更新状态显示
                if (data && data.state) {
                    updateStateDisplay(data.state);
                }

                console.log('Text Replacement Response:', event.data);
            }
        });

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateStatus('页面加载完成，准备就绪', 'success');
        });
    </script>
</body>
</html>
