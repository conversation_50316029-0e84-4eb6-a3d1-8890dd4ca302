# PDF.js 文本替换功能增强版

基于PDF.js开源代码实现的完整文本替换预览功能，特别针对iframe_full_features.html文件进行了全面增强。

## 🚀 新增功能特性

### 1. 撤销/重做机制
- ✅ **完整的历史记录管理**：支持最多50步操作历史
- ✅ **单步撤销/重做**：可以逐步撤销或重做文本替换操作
- ✅ **批量操作撤销**：支持批量操作的整体撤销
- ✅ **状态持久化**：使用sessionStorage保持撤销历史

### 2. 状态管理系统
- ✅ **实时状态监控**：显示当前替换数量、历史位置等信息
- ✅ **会话持久化**：页面刷新后保持替换状态
- ✅ **状态查询API**：提供完整的状态信息查询接口
- ✅ **事件驱动更新**：基于事件总线的状态同步

### 3. 增强的参数接收机制
- ✅ **标准化配置接口**：支持TextReplacementConfig标准格式
- ✅ **双向参数接收**：URL参数 + postMessage动态配置
- ✅ **向后兼容**：完全兼容现有的参数格式
- ✅ **全局样式支持**：支持全局样式配置

### 4. 响应式支持增强
- ✅ **智能事件监听**：监听缩放、旋转、调整大小事件
- ✅ **防抖更新机制**：避免频繁更新影响性能
- ✅ **多层级响应**：页面级、文档级、窗口级响应
- ✅ **自动重新计算**：变换后自动重新计算文本位置

### 5. 完整的API接口
- ✅ **RESTful风格API**：标准化的请求/响应格式
- ✅ **错误处理机制**：完整的错误捕获和反馈
- ✅ **请求ID追踪**：支持异步请求的状态追踪
- ✅ **批量操作支持**：高效的批量文本替换

### 6. 高级功能
- ✅ **批量操作模式**：开始/结束批量操作，减少历史记录
- ✅ **智能验证**：完整的参数验证和错误提示
- ✅ **性能优化**：防抖、节流、延迟渲染等优化
- ✅ **调试支持**：详细的日志记录和状态监控

## 📋 API 接口文档

### 标准化配置格式

```typescript
interface TextReplacementConfig {
  replacements: Array<{
    page: number;                    // 页码（从1开始）
    originalText: string;            // 原始文本
    replacementText: string;         // 替换文本
    position?: {                     // 可选：精确位置
      x: number;
      y: number;
      width: number;
      height: number;
    };
    style?: {                        // 可选：样式配置
      backgroundColor: string;
      textColor: string;
      fontSize?: string;
      border?: string;
    };
  }>;
  globalStyle?: {                    // 可选：全局样式
    highlightColor: string;
    opacity: number;
  };
  pdfFile?: string;                  // 可选：PDF文件路径
}
```

### 外部调用API

```javascript
// 1. 设置文本替换（新格式）
window.PDFTextReplacementAPI.setTextReplacements({
  replacements: [...],
  globalStyle: { highlightColor: 'rgba(255, 255, 0, 0.3)', opacity: 0.3 },
  pdfFile: 'document.pdf'
});

// 2. 添加单个替换
window.PDFTextReplacementAPI.addTextReplacement({
  page: 1,
  originalText: "Hello",
  replacementText: "你好",
  style: { backgroundColor: "rgba(255, 255, 0, 0.3)" }
});

// 3. 撤销/重做操作
window.PDFTextReplacementAPI.undo();
window.PDFTextReplacementAPI.redo();

// 4. 批量操作
window.PDFTextReplacementAPI.startBatch();
// ... 添加多个替换 ...
window.PDFTextReplacementAPI.endBatch();

// 5. 状态管理
window.PDFTextReplacementAPI.getState();
window.PDFTextReplacementAPI.clearTextReplacements();

// 6. 事件监听
window.PDFTextReplacementAPI.addEventListener('textReplacementAdded', (event) => {
  console.log('替换已添加:', event.data);
});
```

### PostMessage API

```javascript
// 发送消息到iframe
iframe.contentWindow.postMessage({
  type: 'setTextReplacements',
  data: {
    replacements: [...],
    globalStyle: {...}
  },
  requestId: 'unique-request-id'
}, '*');

// 监听响应
window.addEventListener('message', (event) => {
  if (event.data.type === 'textReplacementResponse') {
    console.log('响应:', event.data);
  }
});
```

## 🧪 测试用例

### 1. 基础功能测试
- 单个文本替换
- 多个文本替换
- 跨元素文本替换
- 复杂文段替换

### 2. 撤销/重做测试
- 单步撤销/重做
- 多步撤销/重做
- 批量操作撤销
- 历史边界测试

### 3. 响应式测试
- 缩放测试（25%-500%）
- 旋转测试（0°/90°/180°/270°）
- 窗口调整大小测试
- 页面滚动测试

### 4. 错误处理测试
- 无效参数测试
- 网络错误测试
- 边界条件测试
- 异常恢复测试

### 5. 性能测试
- 大量替换性能测试
- 频繁操作性能测试
- 内存泄漏测试
- 响应时间测试

## 🎯 使用示例

### 基础使用

```html
<!-- 1. 通过URL参数 -->
<iframe src="iframe_full_features.html?textReplacements=[...]"></iframe>

<!-- 2. 通过postMessage -->
<script>
const config = {
  replacements: [
    {
      page: 1,
      originalText: "JavaScript",
      replacementText: "TypeScript",
      style: {
        backgroundColor: "rgba(255, 255, 0, 0.3)",
        color: "#000000"
      }
    }
  ]
};

iframe.contentWindow.postMessage({
  type: 'setTextReplacements',
  data: config
}, '*');
</script>
```

### 高级使用

```javascript
// 批量操作示例
async function performBatchReplacements() {
  // 开始批量模式
  window.PDFTextReplacementAPI.startBatch();
  
  // 添加多个替换
  const replacements = [
    { page: 1, originalText: "Hello", replacementText: "你好" },
    { page: 1, originalText: "World", replacementText: "世界" },
    { page: 2, originalText: "PDF", replacementText: "文档" }
  ];
  
  replacements.forEach(replacement => {
    window.PDFTextReplacementAPI.addTextReplacement(replacement);
  });
  
  // 结束批量模式（这时才会保存到历史记录）
  window.PDFTextReplacementAPI.endBatch();
}

// 状态监控示例
window.PDFTextReplacementAPI.addEventListener('all', (event) => {
  console.log('状态变化:', event);
  updateUI(event.data.state);
});
```

## 🔧 技术实现

### 核心架构
1. **TextReplacementManager**：核心管理类，处理所有文本替换逻辑
2. **历史管理系统**：基于快照的撤销/重做实现
3. **事件驱动架构**：使用EventBus进行组件间通信
4. **响应式更新系统**：智能监听页面变换事件

### 关键技术点
- **文本定位算法**：基于PDF.js textLayer的精确定位
- **跨元素匹配**：处理被分割到多个DOM元素的文本
- **坐标系转换**：PDF坐标到DOM坐标的精确转换
- **性能优化**：防抖、节流、虚拟化等技术

### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ 新增完整的撤销/重做机制
- ✅ 新增状态管理和持久化
- ✅ 增强参数接收机制
- ✅ 完善响应式支持
- ✅ 新增完整的API接口和错误处理
- ✅ 新增批量操作支持
- ✅ 新增完整的测试用例

### v1.0.0 (基础版本)
- ✅ 基础文本替换功能
- ✅ URL参数支持
- ✅ 基础样式支持
- ✅ 简单的postMessage通信

## 🚀 快速开始

1. **构建项目**
```bash
npx gulp clean
npx gulp generic
```

2. **打开测试页面**
```
http://localhost:8080/complex_text_replacement_test.html
```

3. **测试功能**
- 点击各种测试按钮体验功能
- 使用撤销/重做按钮测试历史管理
- 在PDF查看器中测试缩放和旋转
- 查看状态信息面板了解当前状态

## 📞 技术支持

如有问题或建议，请查看：
- `TEXT_REPLACEMENT_README.md` - 基础功能文档
- `COMPLEX_TEXT_REPLACEMENT_GUIDE.md` - 复杂文段处理指南
- `complex_text_replacement_test.html` - 完整功能演示
