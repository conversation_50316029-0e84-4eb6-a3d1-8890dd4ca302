# 复杂文段替换功能使用指南

## 功能概述

我们已经成功实现了跨元素的复杂文段替换功能，可以处理PDF中被分割到多个DOM元素的长文本。

## 核心改进

### 1. 智能文本匹配算法
- **跨元素匹配**：能够识别跨多个DOM元素的文本
- **文本标准化**：自动去除空格、标准化标点符号
- **连续文本构建**：将所有文本元素连接成连续字符串进行匹配

### 2. 精确边界框计算与覆盖优化
- **多元素边界框**：计算所有涉及元素的最小包围矩形
- **Range API精确定位**：使用浏览器Range API获取文本的精确边界
- **自适应padding**：基于字体大小动态调整覆盖区域padding
- **完全覆盖保证**：确保替换文字完全覆盖原文，不留漏出部分
- **样式继承**：从参考元素继承字体样式

### 3. 覆盖层优化
- **智能padding计算**：基础padding + 字体大小的10%
- **边界框扩展**：自动扩展边界以确保完全覆盖
- **精确文本边界**：使用Range API获取文本的实际渲染边界

### 4. 缩放适配优化
- **缩放感知定位**：自动检测页面缩放级别并调整替换元素位置
- **相对坐标系统**：使用相对于页面的坐标而非绝对像素位置
- **动态更新机制**：缩放变化时自动重新计算和更新替换元素位置
- **清除重建策略**：缩放时先清除旧元素再重新创建，避免位置偏移

## 测试方法

### 方法1：使用专用测试页面
打开 `complex_text_replacement_test.html` 进行测试：

```html
<!-- 包含多个预设的复杂文段测试用例 -->
- 政府采购条例文段
- 长标题替换
- 作者列表替换
- 技术术语替换
- 混合测试
```

### 方法1.5：缩放功能测试
打开 `zoom_test.html` 进行缩放测试：

```html
<!-- 专门测试缩放功能的页面 -->
- 多级缩放测试（50% - 200%）
- 实时缩放调整
- 位置准确性验证
- 覆盖完整性检查
```

### 方法2：直接URL测试
```javascript
const complexReplacement = {
    page: 1,
    originalText: "深圳经济特区政府采购条例》第五十七条 供应商在政府采购中，有下列行为之一的，一至",
    replacementText: "【政府采购违规行为条款】",
    style: {
        backgroundColor: "rgba(255, 0, 0, 0.2)",
        color: "#000000",
        border: "2px solid rgba(255, 0, 0, 0.8)",
        fontWeight: "bold"
    }
};

const url = `build/generic/web/viewer.html?file=your-document.pdf&textReplacements=${encodeURIComponent(JSON.stringify([complexReplacement]))}`;
```

### 方法3：iframe集成测试
```html
<iframe id="pdfViewer" src="iframe_full_features.html"></iframe>
<script>
document.getElementById('pdfViewer').contentWindow.postMessage({
    type: 'textReplacements',
    replacements: [complexReplacement],
    pdfFile: 'your-document.pdf'
}, '*');
</script>
```

## 技术实现细节

### 文本匹配流程
1. **单元素匹配**：首先尝试在单个文本元素中查找
2. **跨元素匹配**：如果单元素匹配失败，构建连续文本进行匹配
3. **文本清理**：去除空格、标准化标点符号
4. **位置映射**：将匹配位置映射回原始DOM元素

### 边界框计算与覆盖优化
```javascript
// 计算所有涉及元素的边界框，带自适应padding
let minLeft = Infinity, minTop = Infinity;
let maxRight = -Infinity, maxBottom = -Infinity;
let maxFontSize = 0;

for (const mapping of involvedElements) {
    const rect = mapping.element.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(mapping.element);
    const fontSize = parseFloat(computedStyle.fontSize) || 12;

    // 更新边界框坐标
    minLeft = Math.min(minLeft, rect.left);
    minTop = Math.min(minTop, rect.top);
    maxRight = Math.max(maxRight, rect.right);
    maxBottom = Math.max(maxBottom, rect.bottom);

    if (fontSize > maxFontSize) maxFontSize = fontSize;
}

// 添加自适应padding确保完全覆盖
const basePadding = 2;
const fontPadding = Math.max(1, maxFontSize * 0.1); // 字体大小的10%
const padding = basePadding + fontPadding;

minLeft -= padding;
minTop -= padding;
maxRight += padding;
maxBottom += padding;
```

### 精确文本边界检测
```javascript
// 使用Range API获取精确的文本边界
function getPreciseTextBounds(element, targetText, layerRect) {
    const textContent = element.textContent || '';
    const targetIndex = textContent.indexOf(targetText);

    if (targetIndex !== -1) {
        const range = document.createRange();
        const textNode = element.firstChild;

        if (textNode && textNode.nodeType === Node.TEXT_NODE) {
            range.setStart(textNode, targetIndex);
            range.setEnd(textNode, targetIndex + targetText.length);

            const rangeRect = range.getBoundingClientRect();
            return {
                left: rangeRect.left - layerRect.left,
                top: rangeRect.top - layerRect.top,
                width: rangeRect.width,
                height: rangeRect.height
            };
        }
    }

    // 回退到元素边界
    const rect = element.getBoundingClientRect();
    return {
        left: rect.left - layerRect.left,
        top: rect.top - layerRect.top,
        width: rect.width,
        height: rect.height
    };
}
```

## 支持的复杂场景

### 1. 跨行文本
```javascript
{
    originalText: "这是一段很长的文本，可能会被PDF渲染引擎分割到多个DOM元素中",
    replacementText: "【长文本替换】"
}
```

### 2. 包含标点符号的文本
```javascript
{
    originalText: "《深圳经济特区政府采购条例》第五十七条",
    replacementText: "【政府采购条例】"
}
```

### 3. 英文长句
```javascript
{
    originalText: "TraceMonkey: Fast, Native Code Generation for Dynamic Languages",
    replacementText: "TraceMonkey：动态语言的快速原生代码生成"
}
```

### 4. 作者列表
```javascript
{
    originalText: "Andreas Gal, Brendan Eich, Mike Shaver, David Anderson, David Mandelin",
    replacementText: "作者：Andreas Gal等人"
}
```

## 样式配置

### 基础样式
```javascript
style: {
    backgroundColor: "rgba(255, 255, 0, 0.3)",
    color: "#000000",
    border: "1px solid rgba(255, 255, 0, 0.8)"
}
```

### 高级样式
```javascript
style: {
    backgroundColor: "rgba(255, 0, 0, 0.2)",
    color: "#000000",
    border: "2px solid rgba(255, 0, 0, 0.8)",
    borderRadius: "4px",
    fontWeight: "bold",
    fontSize: "1.1em",
    overlayColor: "#ffffff" // 覆盖层颜色
}
```

## 调试技巧

### 1. 启用调试日志
```javascript
// 在浏览器控制台中执行
localStorage.setItem('pdfjs.verbosity', '5');
```

### 2. 检查文本匹配
- 打开浏览器开发者工具
- 查看控制台中的匹配日志
- 检查是否有"Original text not found"警告

### 3. 验证文本内容
```javascript
// 获取页面所有文本内容
const textDivs = document.querySelectorAll('span[role="presentation"]');
const allText = Array.from(textDivs).map(div => div.textContent).join('');
console.log('页面文本内容：', allText);
```

## 性能优化

### 1. 文本匹配优化
- 优先进行单元素匹配（性能更好）
- 只有在必要时才进行跨元素匹配
- 使用文本清理减少匹配复杂度

### 2. 渲染优化
- 批量创建DOM元素
- 使用CSS transform而非重新计算位置
- 避免频繁的DOM查询

## 常见问题

### Q1: 复杂文段替换不成功
**解决方案**：
1. 检查原文是否完全匹配（包括标点符号）
2. 尝试简化原文，去除特殊字符
3. 使用浏览器开发者工具检查实际的文本内容

### Q2: 替换位置不准确或有漏出部分
**解决方案**：
1. 确保PDF已完全加载
2. 检查页面缩放级别
3. 验证文本层是否正确渲染
4. 新版本已自动优化覆盖区域，使用自适应padding确保完全覆盖
5. 如仍有问题，可在样式中增加overlayColor的不透明度

### Q3: 缩放后文本替换位置偏移
**解决方案**：
1. 新版本已实现缩放感知定位，自动适配不同缩放级别
2. 使用 `zoom_test.html` 测试缩放功能
3. 确保在缩放变化后替换元素会自动重新定位
4. 如果仍有偏移，检查浏览器控制台是否有错误信息

### Q3: 性能问题
**解决方案**：
1. 减少同时替换的文本数量
2. 避免过于复杂的正则表达式
3. 使用简化的样式配置

## 示例代码

### 完整示例
```javascript
const complexReplacements = [
    {
        page: 1,
        originalText: "深圳经济特区政府采购条例》第五十七条 供应商在政府采购中，有下列行为之一的，一至",
        replacementText: "【政府采购违规行为条款】",
        style: {
            backgroundColor: "rgba(255, 0, 0, 0.2)",
            color: "#000000",
            border: "2px solid rgba(255, 0, 0, 0.8)",
            fontWeight: "bold"
        }
    },
    {
        page: 1,
        originalText: "TraceMonkey: Fast, Native Code Generation for Dynamic Languages",
        replacementText: "TraceMonkey：动态语言的快速原生代码生成",
        style: {
            backgroundColor: "rgba(0, 123, 255, 0.2)",
            color: "#000000",
            border: "2px solid rgba(0, 123, 255, 0.8)",
            fontWeight: "bold",
            fontSize: "1.1em"
        }
    }
];

// 加载PDF并应用替换
const encodedReplacements = encodeURIComponent(JSON.stringify(complexReplacements));
const url = `build/generic/web/viewer.html?file=document.pdf&textReplacements=${encodedReplacements}`;
document.getElementById('pdfViewer').src = url;
```

现在您可以测试复杂文段的替换功能了！建议先使用 `complex_text_replacement_test.html` 页面进行测试，然后根据需要调整配置。
