/* Copyright 2024 PDF.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { RenderingStates } from "./ui_utils.js";

/**
 * TextReplacementManager handles text replacement data passed via URL parameters
 * and renders replacement overlays on the PDF pages.
 */
class TextReplacementManager {
  #replacements = new Map(); // pageNumber -> replacements array
  #pageViews = new Map(); // pageNumber -> PDFPageView
  #enabled = false;

  constructor() {
    this.#parseReplacementsFromURL();

    // Debug logging
    if (this.#enabled) {
      console.log('TextReplacementManager: Initialized with', this.#replacements.size, 'pages of replacements');
      for (const [pageNum, replacements] of this.#replacements) {
        console.log(`TextReplacementManager: Page ${pageNum} has ${replacements.length} replacements`);
      }
    }
  }

  /**
   * Parse text replacement data from URL parameters
   * Expected format: ?textReplacements=[{"page":1,"originalText":"old","replacementText":"new","style":{"backgroundColor":"yellow"}}]
   */
  #parseReplacementsFromURL() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const replacementsParam = urlParams.get('textReplacements');
      
      if (replacementsParam) {
        const replacementsData = JSON.parse(decodeURIComponent(replacementsParam));
        
        if (Array.isArray(replacementsData)) {
          for (const replacement of replacementsData) {
            if (this.#isValidReplacement(replacement)) {
              const pageNum = replacement.page;
              if (!this.#replacements.has(pageNum)) {
                this.#replacements.set(pageNum, []);
              }
              this.#replacements.get(pageNum).push(replacement);
            }
          }
          this.#enabled = this.#replacements.size > 0;
        }
      }
    } catch (error) {
      console.error('TextReplacementManager: Error parsing URL parameters:', error);
    }
  }

  /**
   * Validate replacement object structure
   */
  #isValidReplacement(replacement) {
    return (
      replacement &&
      typeof replacement.page === 'number' &&
      replacement.page > 0 &&
      typeof replacement.originalText === 'string' &&
      replacement.originalText.length > 0 &&
      typeof replacement.replacementText === 'string'
    );
  }

  /**
   * Check if manager is enabled
   */
  get enabled() {
    return this.#enabled;
  }

  /**
   * Check if there are replacements for a specific page
   */
  hasReplacementsForPage(pageNumber) {
    return this.#replacements.has(pageNumber);
  }

  /**
   * Get replacements for a specific page
   */
  getReplacementsForPage(pageNumber) {
    return this.#replacements.get(pageNumber) || [];
  }

  /**
   * Register a page view for replacement rendering
   */
  registerPageView(pageNumber, pageView) {
    console.log(`TextReplacementManager: Registering page ${pageNumber}`);
    this.#pageViews.set(pageNumber, pageView);

    // If there are replacements for this page, render them
    if (this.hasReplacementsForPage(pageNumber)) {
      console.log(`TextReplacementManager: Found replacements for page ${pageNumber}, rendering...`);
      this.#renderReplacementsForPage(pageNumber);
    } else {
      console.log(`TextReplacementManager: No replacements for page ${pageNumber}`);
    }
  }

  /**
   * Unregister a page view
   */
  unregisterPageView(pageNumber) {
    this.#pageViews.delete(pageNumber);
  }

  /**
   * Render replacements for a specific page
   */
  #renderReplacementsForPage(pageNumber) {
    const pageView = this.#pageViews.get(pageNumber);
    const replacements = this.getReplacementsForPage(pageNumber);
    
    if (!pageView || !replacements.length) {
      return;
    }

    // Wait for the page and text layer to be rendered
    if (pageView.renderingState !== RenderingStates.FINISHED || !pageView.textLayer) {
      // Listen for text layer rendered event
      const onTextLayerRendered = (evt) => {
        if (evt.pageNumber === pageNumber) {
          this.#createReplacementElements(pageView, replacements);
          // Remove the event listener after use
          pageView.eventBus.off('textlayerrendered', onTextLayerRendered);
        }
      };
      pageView.eventBus.on('textlayerrendered', onTextLayerRendered);
    } else {
      this.#createReplacementElements(pageView, replacements);
    }
  }

  /**
   * Create replacement DOM elements for a page
   */
  #createReplacementElements(pageView, replacements) {
    const container = pageView.div;
    const textLayer = pageView.textLayer;
    
    if (!textLayer || !textLayer.div) {
      console.warn('TextReplacementManager: Text layer not available for page', pageView.id);
      return;
    }

    // Remove existing text replacements
    const existingReplacements = container.querySelectorAll('.text-replacement');
    existingReplacements.forEach(el => el.remove());

    // Create replacement layer if it doesn't exist
    let replacementLayer = container.querySelector('.text-replacement-layer');
    if (!replacementLayer) {
      replacementLayer = document.createElement('div');
      replacementLayer.className = 'text-replacement-layer';
      replacementLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 3;
      `;
      container.appendChild(replacementLayer);
    }

    // Process each replacement
    for (const replacement of replacements) {
      this.#processTextReplacement(replacement, textLayer, replacementLayer, pageView);
    }
  }

  /**
   * Process a single text replacement
   */
  #processTextReplacement(replacement, textLayer, replacementLayer, pageView) {
    const { originalText, replacementText } = replacement;
    
    // Find text elements that contain the original text
    const textElements = this.#findTextElements(originalText, textLayer);
    
    if (textElements.length === 0) {
      console.warn(`TextReplacementManager: Original text "${originalText}" not found on page ${pageView.id}`);
      return;
    }

    // Create replacement elements for each found text element
    for (const textElement of textElements) {
      if (textElement.matchType === 'single') {
        this.#createSingleElementReplacement(textElement, replacement, replacementLayer);
      } else if (textElement.matchType === 'cross-element') {
        this.#createCrossElementReplacement(textElement, replacement, replacementLayer);
      }
    }
  }

  /**
   * Find text elements containing the target text
   * Supports both single-element and cross-element text matching
   */
  #findTextElements(targetText, textLayer) {
    const textElements = [];
    const textDivs = Array.from(textLayer.div.querySelectorAll('span[role="presentation"]'));

    // First try simple single-element matching
    for (const textDiv of textDivs) {
      if (textDiv.textContent && textDiv.textContent.includes(targetText)) {
        textElements.push({
          elements: [textDiv],
          text: textDiv.textContent,
          targetText: targetText,
          matchType: 'single'
        });
      }
    }

    // If no single-element matches found, try cross-element matching
    if (textElements.length === 0) {
      const crossElementMatches = this.#findCrossElementText(targetText, textDivs);
      textElements.push(...crossElementMatches);
    }

    return textElements;
  }

  /**
   * Find text that spans across multiple elements
   */
  #findCrossElementText(targetText, textDivs) {
    const matches = [];
    const cleanTargetText = this.#cleanText(targetText);

    // Build continuous text from all elements
    let continuousText = '';
    const elementMap = [];

    for (let i = 0; i < textDivs.length; i++) {
      const element = textDivs[i];
      const text = element.textContent || '';
      const cleanText = this.#cleanText(text);

      if (cleanText) {
        const startIndex = continuousText.length;
        continuousText += cleanText;
        elementMap.push({
          element: element,
          startIndex: startIndex,
          endIndex: continuousText.length,
          originalText: text,
          cleanText: cleanText
        });
      }
    }

    // Find matches in continuous text
    let searchIndex = 0;
    while (searchIndex < continuousText.length) {
      const matchIndex = continuousText.indexOf(cleanTargetText, searchIndex);
      if (matchIndex === -1) break;

      const matchEndIndex = matchIndex + cleanTargetText.length;

      // Find which elements contain this match
      const involvedElements = [];
      for (const mapping of elementMap) {
        if (mapping.startIndex < matchEndIndex && mapping.endIndex > matchIndex) {
          involvedElements.push(mapping);
        }
      }

      if (involvedElements.length > 0) {
        matches.push({
          elements: involvedElements.map(m => m.element),
          text: targetText,
          targetText: targetText,
          matchType: 'cross-element',
          matchStart: matchIndex,
          matchEnd: matchEndIndex,
          involvedElements: involvedElements
        });
      }

      searchIndex = matchIndex + 1;
    }

    return matches;
  }

  /**
   * Clean text for better matching (remove extra spaces, normalize punctuation)
   */
  #cleanText(text) {
    return text
      .replace(/\s+/g, '') // Remove all whitespace
      .replace(/[""'']/g, '"') // Normalize quotes
      .replace(/[，。；：！？]/g, match => {
        // Normalize Chinese punctuation
        const map = {'，': ',', '。': '.', '；': ';', '：': ':', '！': '!', '？': '?'};
        return map[match] || match;
      })
      .toLowerCase();
  }

  /**
   * Create a replacement element for a text element
   */
  #createReplacementElement(textElement, replacement, replacementLayer) {
    // Handle both old and new data structures
    const element = textElement.element || (textElement.elements && textElement.elements[0]);
    const targetText = textElement.targetText;
    const { replacementText, style = {} } = replacement;
    
    // Get the position and size of the original text element
    const rect = element.getBoundingClientRect();
    const layerRect = replacementLayer.getBoundingClientRect();
    
    // Calculate relative position
    const left = rect.left - layerRect.left;
    const top = rect.top - layerRect.top;
    const width = rect.width;
    const height = rect.height;

    // Create overlay element to cover original text
    const overlay = document.createElement('div');
    overlay.className = 'text-replacement text-replacement-overlay';
    overlay.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      background-color: ${style.backgroundColor || '#ffffff'};
      z-index: 1;
      pointer-events: none;
    `;

    // Create replacement text element
    const replacementElement = document.createElement('div');
    replacementElement.className = 'text-replacement text-replacement-text';
    replacementElement.textContent = replacementText;
    
    // Copy font styles from original element
    const computedStyle = window.getComputedStyle(element);
    replacementElement.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      font-family: ${computedStyle.fontFamily};
      font-size: ${computedStyle.fontSize};
      font-weight: ${computedStyle.fontWeight};
      color: ${style.color ||   '#000000'};
      background-color: ${style.backgroundColor || '#fff'};
      border: ${style.border || '1px solid #fff'};
      border-radius: 2px;
      padding: 0;
      margin: 0;
      line-height: ${computedStyle.lineHeight};
      text-align: ${computedStyle.textAlign};
      z-index: 2;
      pointer-events: none;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: ${computedStyle.textAlign === 'center' ? 'center' : 'flex-start'};
    `;

    // Apply custom styles
    if (style.fontSize) replacementElement.style.fontSize = style.fontSize;
    if (style.fontWeight) replacementElement.style.fontWeight = style.fontWeight;
    if (style.fontFamily) replacementElement.style.fontFamily = style.fontFamily;

    // Add elements to replacement layer
    replacementLayer.appendChild(overlay);
    replacementLayer.appendChild(replacementElement);

    // Add data attributes for identification
    overlay.setAttribute('data-page', replacement.page);
    overlay.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-page', replacement.page);
    replacementElement.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-replacement-text', replacementText);
  }

  /**
   * Update replacements when page is transformed (zoom, rotate, etc.)
   */
  updateReplacements(pageNumber) {
    if (!this.hasReplacementsForPage(pageNumber)) {
      return;
    }

    const pageView = this.#pageViews.get(pageNumber);
    if (!pageView) {
      return;
    }

    // Clear existing replacement elements first
    this.#clearReplacementsForPage(pageNumber);

    // Re-render replacements with updated coordinates
    const replacements = this.getReplacementsForPage(pageNumber);
    this.#createReplacementElements(pageView, replacements);
  }

  /**
   * Clear replacement elements for a specific page
   */
  #clearReplacementsForPage(pageNumber) {
    const pageView = this.#pageViews.get(pageNumber);
    if (!pageView || !pageView.div) {
      return;
    }

    // Remove all replacement elements from this page
    const replacementElements = pageView.div.querySelectorAll('.text-replacement');
    replacementElements.forEach(element => {
      element.remove();
    });
  }

  /**
   * Add new replacements programmatically
   */
  addReplacements(newReplacements) {
    if (!Array.isArray(newReplacements)) {
      return;
    }

    for (const replacement of newReplacements) {
      if (this.#isValidReplacement(replacement)) {
        const pageNum = replacement.page;
        if (!this.#replacements.has(pageNum)) {
          this.#replacements.set(pageNum, []);
        }
        this.#replacements.get(pageNum).push(replacement);
        
        // Render if page is already loaded
        if (this.#pageViews.has(pageNum)) {
          this.#renderReplacementsForPage(pageNum);
        }
      }
    }
    
    this.#enabled = this.#replacements.size > 0;
  }

  /**
   * Clear all replacements
   */
  clearReplacements() {
    // Remove all replacement elements from DOM
    for (const [pageNumber] of this.#pageViews) {
      const pageView = this.#pageViews.get(pageNumber);
      if (pageView) {
        const container = pageView.div;
        const existingReplacements = container.querySelectorAll('.text-replacement');
        existingReplacements.forEach(el => el.remove());
      }
    }

    this.#replacements.clear();
    this.#enabled = false;
  }

  /**
   * Create a replacement element for a single text element
   */
  #createSingleElementReplacement(textElement, replacement, replacementLayer) {
    const { elements, targetText } = textElement;
    const element = elements[0]; // Single element
    const { replacementText, style = {} } = replacement;

    // Get precise text bounds with scale awareness
    const bounds = this.#getPreciseTextBounds(element, targetText, replacementLayer);
    const computedStyle = window.getComputedStyle(element);
    const fontSize = parseFloat(computedStyle.fontSize) || 12;

    // Calculate position with adaptive padding for better coverage
    const basePadding = 2;
    const fontPadding = Math.max(1, fontSize * 0.1); // 10% of font size
    const padding = (basePadding + fontPadding) / (bounds.scale || 1); // Adjust padding for scale

    const left = bounds.left - padding;
    const top = bounds.top - padding;
    const width = bounds.width + (padding * 2);
    const height = bounds.height + (padding * 2);

    this.#createReplacementElementsAtPosition(left, top, width, height, element, replacement, replacementLayer, targetText);
  }

  /**
   * Create a replacement element for cross-element text
   */
  #createCrossElementReplacement(textElement, replacement, replacementLayer) {
    const { elements, targetText, involvedElements } = textElement;
    const { replacementText, style = {} } = replacement;

    if (!involvedElements || involvedElements.length === 0) {
      console.warn('TextReplacementManager: No involved elements for cross-element replacement');
      return;
    }

    // Calculate bounding box for all involved elements with scale awareness
    let minLeft = Infinity, minTop = Infinity, maxRight = -Infinity, maxBottom = -Infinity;
    let firstElement = null;
    let maxFontSize = 0;
    let scale = 1;

    // Get scale from the first element
    if (involvedElements.length > 0) {
      const pageView = replacementLayer.closest('.page');
      if (pageView) {
        const transform = pageView.style.transform;
        const scaleMatch = transform.match(/scale\(([^)]+)\)/);
        if (scaleMatch) {
          scale = parseFloat(scaleMatch[1]) || 1;
        }
      }
    }

    const layerRect = replacementLayer.getBoundingClientRect();

    for (const mapping of involvedElements) {
      const bounds = this.#convertToRelativePosition(
        mapping.element.getBoundingClientRect(),
        replacementLayer
      );
      const computedStyle = window.getComputedStyle(mapping.element);
      const fontSize = parseFloat(computedStyle.fontSize) || 12;

      const left = bounds.left;
      const top = bounds.top;
      const right = left + bounds.width;
      const bottom = top + bounds.height;

      if (left < minLeft) minLeft = left;
      if (top < minTop) minTop = top;
      if (right > maxRight) maxRight = right;
      if (bottom > maxBottom) maxBottom = bottom;

      if (fontSize > maxFontSize) maxFontSize = fontSize;
      if (!firstElement) firstElement = mapping.element;
    }

    // Add adaptive padding based on font size and scale
    const basePadding = 2;
    const fontPadding = Math.max(1, maxFontSize * 0.1); // 10% of font size
    const padding = (basePadding + fontPadding) / scale; // Adjust for scale

    minLeft -= padding;
    minTop -= padding;
    maxRight += padding;
    maxBottom += padding;

    const width = maxRight - minLeft;
    const height = maxBottom - minTop;

    this.#createReplacementElementsAtPosition(minLeft, minTop, width, height, firstElement, replacement, replacementLayer, targetText);
  }

  /**
   * Get precise text bounds using Range API with scale awareness
   */
  #getPreciseTextBounds(element, targetText, replacementLayer) {
    try {
      const textContent = element.textContent || '';
      const targetIndex = textContent.indexOf(targetText);

      if (targetIndex === -1) {
        // Fallback to element bounds
        return this.#getElementRelativeBounds(element, replacementLayer);
      }

      // Create range for the target text
      const range = document.createRange();
      const textNode = element.firstChild;

      if (textNode && textNode.nodeType === Node.TEXT_NODE) {
        range.setStart(textNode, targetIndex);
        range.setEnd(textNode, targetIndex + targetText.length);

        const rangeRect = range.getBoundingClientRect();
        if (rangeRect.width > 0 && rangeRect.height > 0) {
          return this.#convertToRelativePosition(rangeRect, replacementLayer);
        }
      }
    } catch (error) {
      console.warn('TextReplacementManager: Error getting precise text bounds:', error);
    }

    // Fallback to element bounds
    return this.#getElementRelativeBounds(element, replacementLayer);
  }

  /**
   * Get element bounds relative to replacement layer
   */
  #getElementRelativeBounds(element, replacementLayer) {
    const rect = element.getBoundingClientRect();
    return this.#convertToRelativePosition(rect, replacementLayer);
  }

  /**
   * Convert absolute position to position relative to replacement layer
   */
  #convertToRelativePosition(rect, replacementLayer) {
    const layerRect = replacementLayer.getBoundingClientRect();

    // Get the page view container to calculate scale
    const pageView = replacementLayer.closest('.page');
    let scale = 1;

    if (pageView) {
      const transform = pageView.style.transform;
      const scaleMatch = transform.match(/scale\(([^)]+)\)/);
      if (scaleMatch) {
        scale = parseFloat(scaleMatch[1]) || 1;
      }
    }

    return {
      left: (rect.left - layerRect.left) / scale,
      top: (rect.top - layerRect.top) / scale,
      width: rect.width / scale,
      height: rect.height / scale,
      scale: scale
    };
  }

  /**
   * Create the actual replacement DOM elements at specified position
   */
  #createReplacementElementsAtPosition(left, top, width, height, referenceElement, replacement, replacementLayer, targetText) {
    const { replacementText, style = {} } = replacement;

    // Create overlay element to cover original text
    const overlay = document.createElement('div');
    overlay.className = 'text-replacement text-replacement-overlay';
    overlay.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      background-color: ${style.overlayColor || '#ffffff'};
      z-index: 1;
      pointer-events: none;
      box-sizing: border-box;
      border-radius: 1px;
    `;

    // Create replacement text element
    const replacementElement = document.createElement('div');
    replacementElement.className = 'text-replacement text-replacement-text';
    replacementElement.textContent = replacementText;

    // Copy font styles from reference element
    const computedStyle = window.getComputedStyle(referenceElement);
    replacementElement.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      font-family: ${computedStyle.fontFamily};
      font-size: ${computedStyle.fontSize};
      font-weight: ${computedStyle.fontWeight};
      color: ${style.color || '#000000'};
      background-color: ${style.backgroundColor || 'rgba(255, 255, 0, 0.3)'};
      border: ${style.border || '1px solid rgba(255, 255, 0, 0.8)'};
      border-radius: 2px;
      padding: 1px 2px;
      margin: 0;
      line-height: ${computedStyle.lineHeight};
      text-align: ${computedStyle.textAlign};
      z-index: 2;
      pointer-events: none;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: ${computedStyle.textAlign === 'center' ? 'center' : 'flex-start'};
      word-wrap: break-word;
      overflow-wrap: break-word;
    `;

    // Apply custom styles
    if (style.fontSize) replacementElement.style.fontSize = style.fontSize;
    if (style.fontWeight) replacementElement.style.fontWeight = style.fontWeight;
    if (style.fontFamily) replacementElement.style.fontFamily = style.fontFamily;

    // Add elements to replacement layer
    replacementLayer.appendChild(overlay);
    replacementLayer.appendChild(replacementElement);

    // Add data attributes for identification
    overlay.setAttribute('data-page', replacement.page);
    overlay.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-page', replacement.page);
    replacementElement.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-replacement-text', replacementText);
  }
}

export { TextReplacementManager };
