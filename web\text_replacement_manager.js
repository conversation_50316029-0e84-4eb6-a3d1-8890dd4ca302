/* Copyright 2024 PDF.js Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { RenderingStates } from "./ui_utils.js";

/**
 * TextReplacementManager handles text replacement data passed via URL parameters
 * and renders replacement overlays on the PDF pages.
 * Enhanced with undo/redo functionality and state management.
 */
class TextReplacementManager {
  #replacements = new Map(); // pageNumber -> replacements array
  #pageViews = new Map(); // pageNumber -> PDFPageView
  #enabled = false;

  // Undo/Redo system
  #history = []; // Array of state snapshots
  #historyIndex = -1; // Current position in history
  #maxHistorySize = 50; // Maximum number of undo steps

  // State management
  #sessionStorageKey = 'pdfjs_text_replacements';
  #eventBus = null;

  // Batch operation support
  #batchMode = false;
  #batchOperations = [];

  // Responsive update support
  #scaleUpdateTimeout = null;
  #resizeTimeout = null;
  #resizeHandler = null;

  constructor(eventBus = null) {
    this.#eventBus = eventBus;

    // Try to restore from session storage first
    this.#restoreFromSessionStorage();

    // If no stored state, parse from URL
    if (!this.#enabled) {
      this.#parseReplacementsFromURL();
    }

    // Save initial state to history
    this.#saveToHistory();

    // Debug logging
    if (this.#enabled) {
      console.log('TextReplacementManager: Initialized with', this.#replacements.size, 'pages of replacements');
      for (const [pageNum, replacements] of this.#replacements) {
        console.log(`TextReplacementManager: Page ${pageNum} has ${replacements.length} replacements`);
      }
    }

    // Listen for postMessage events for dynamic updates
    this.#setupMessageListener();

    // Setup event bus listeners for responsive updates
    this.#setupEventBusListeners();
  }

  /**
   * Parse text replacement data from URL parameters
   * Expected format: ?textReplacements=[{"page":1,"originalText":"old","replacementText":"new","style":{"backgroundColor":"yellow"}}]
   */
  #parseReplacementsFromURL() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const replacementsParam = urlParams.get('textReplacements');
      
      if (replacementsParam) {
        const replacementsData = JSON.parse(decodeURIComponent(replacementsParam));
        
        if (Array.isArray(replacementsData)) {
          for (const replacement of replacementsData) {
            if (this.#isValidReplacement(replacement)) {
              const pageNum = replacement.page;
              if (!this.#replacements.has(pageNum)) {
                this.#replacements.set(pageNum, []);
              }
              this.#replacements.get(pageNum).push(replacement);
            }
          }
          this.#enabled = this.#replacements.size > 0;
        }
      }
    } catch (error) {
      console.error('TextReplacementManager: Error parsing URL parameters:', error);
    }
  }



  /**
   * Check if manager is enabled
   */
  get enabled() {
    return this.#enabled;
  }

  /**
   * Check if there are replacements for a specific page
   */
  hasReplacementsForPage(pageNumber) {
    return this.#replacements.has(pageNumber);
  }

  /**
   * Get replacements for a specific page
   */
  getReplacementsForPage(pageNumber) {
    return this.#replacements.get(pageNumber) || [];
  }

  /**
   * Register a page view for replacement rendering
   */
  registerPageView(pageNumber, pageView) {
    console.log(`TextReplacementManager: Registering page ${pageNumber}`);
    this.#pageViews.set(pageNumber, pageView);

    // If there are replacements for this page, render them
    if (this.hasReplacementsForPage(pageNumber)) {
      console.log(`TextReplacementManager: Found replacements for page ${pageNumber}, rendering...`);
      this.#renderReplacementsForPage(pageNumber);
    } else {
      console.log(`TextReplacementManager: No replacements for page ${pageNumber}`);
    }
  }

  /**
   * Unregister a page view
   */
  unregisterPageView(pageNumber) {
    this.#pageViews.delete(pageNumber);
  }

  /**
   * Render replacements for a specific page
   */
  #renderReplacementsForPage(pageNumber) {
    const pageView = this.#pageViews.get(pageNumber);
    const replacements = this.getReplacementsForPage(pageNumber);
    
    if (!pageView || !replacements.length) {
      return;
    }

    // Wait for the page and text layer to be rendered
    if (pageView.renderingState !== RenderingStates.FINISHED || !pageView.textLayer) {
      // Listen for text layer rendered event
      const onTextLayerRendered = (evt) => {
        if (evt.pageNumber === pageNumber) {
          this.#createReplacementElements(pageView, replacements);
          // Remove the event listener after use
          pageView.eventBus.off('textlayerrendered', onTextLayerRendered);
        }
      };
      pageView.eventBus.on('textlayerrendered', onTextLayerRendered);
    } else {
      this.#createReplacementElements(pageView, replacements);
    }
  }

  /**
   * Create replacement DOM elements for a page
   */
  #createReplacementElements(pageView, replacements) {
    const container = pageView.div;
    const textLayer = pageView.textLayer;
    
    if (!textLayer || !textLayer.div) {
      console.warn('TextReplacementManager: Text layer not available for page', pageView.id);
      return;
    }

    // Remove existing text replacements
    const existingReplacements = container.querySelectorAll('.text-replacement');
    existingReplacements.forEach(el => el.remove());

    // Create replacement layer if it doesn't exist
    let replacementLayer = container.querySelector('.text-replacement-layer');
    if (!replacementLayer) {
      replacementLayer = document.createElement('div');
      replacementLayer.className = 'text-replacement-layer';
      replacementLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 3;
      `;
      container.appendChild(replacementLayer);
    }

    // Process each replacement
    for (const replacement of replacements) {
      this.#processTextReplacement(replacement, textLayer, replacementLayer, pageView);
    }
  }

  /**
   * Process a single text replacement
   */
  #processTextReplacement(replacement, textLayer, replacementLayer, pageView) {
    const { originalText, replacementText } = replacement;
    
    // Find text elements that contain the original text
    const textElements = this.#findTextElements(originalText, textLayer);
    
    if (textElements.length === 0) {
      console.warn(`TextReplacementManager: Original text "${originalText}" not found on page ${pageView.id}`);
      return;
    }

    // Create replacement elements for each found text element
    for (const textElement of textElements) {
      if (textElement.matchType === 'single') {
        this.#createSingleElementReplacement(textElement, replacement, replacementLayer);
      } else if (textElement.matchType === 'cross-element') {
        this.#createCrossElementReplacement(textElement, replacement, replacementLayer);
      }
    }
  }

  /**
   * Find text elements containing the target text
   * Supports both single-element and cross-element text matching
   */
  #findTextElements(targetText, textLayer) {
    const textElements = [];
    const textDivs = Array.from(textLayer.div.querySelectorAll('span[role="presentation"]'));

    // First try simple single-element matching
    for (const textDiv of textDivs) {
      if (textDiv.textContent && textDiv.textContent.includes(targetText)) {
        textElements.push({
          elements: [textDiv],
          text: textDiv.textContent,
          targetText: targetText,
          matchType: 'single'
        });
      }
    }

    // If no single-element matches found, try cross-element matching
    if (textElements.length === 0) {
      const crossElementMatches = this.#findCrossElementText(targetText, textDivs);
      textElements.push(...crossElementMatches);
    }

    return textElements;
  }

  /**
   * Find text that spans across multiple elements
   */
  #findCrossElementText(targetText, textDivs) {
    const matches = [];
    const cleanTargetText = this.#cleanText(targetText);

    // Build continuous text from all elements
    let continuousText = '';
    const elementMap = [];

    for (let i = 0; i < textDivs.length; i++) {
      const element = textDivs[i];
      const text = element.textContent || '';
      const cleanText = this.#cleanText(text);

      if (cleanText) {
        const startIndex = continuousText.length;
        continuousText += cleanText;
        elementMap.push({
          element: element,
          startIndex: startIndex,
          endIndex: continuousText.length,
          originalText: text,
          cleanText: cleanText
        });
      }
    }

    // Find matches in continuous text
    let searchIndex = 0;
    while (searchIndex < continuousText.length) {
      const matchIndex = continuousText.indexOf(cleanTargetText, searchIndex);
      if (matchIndex === -1) break;

      const matchEndIndex = matchIndex + cleanTargetText.length;

      // Find which elements contain this match
      const involvedElements = [];
      for (const mapping of elementMap) {
        if (mapping.startIndex < matchEndIndex && mapping.endIndex > matchIndex) {
          involvedElements.push(mapping);
        }
      }

      if (involvedElements.length > 0) {
        matches.push({
          elements: involvedElements.map(m => m.element),
          text: targetText,
          targetText: targetText,
          matchType: 'cross-element',
          matchStart: matchIndex,
          matchEnd: matchEndIndex,
          involvedElements: involvedElements
        });
      }

      searchIndex = matchIndex + 1;
    }

    return matches;
  }

  /**
   * Clean text for better matching (remove extra spaces, normalize punctuation)
   */
  #cleanText(text) {
    return text
      .replace(/\s+/g, '') // Remove all whitespace
      .replace(/[""'']/g, '"') // Normalize quotes
      .replace(/[，。；：！？]/g, match => {
        // Normalize Chinese punctuation
        const map = {'，': ',', '。': '.', '；': ';', '：': ':', '！': '!', '？': '?'};
        return map[match] || match;
      })
      .toLowerCase();
  }

  /**
   * Create a replacement element for a text element
   */
  #createReplacementElement(textElement, replacement, replacementLayer) {
    // Handle both old and new data structures
    const element = textElement.element || (textElement.elements && textElement.elements[0]);
    const targetText = textElement.targetText;
    const { replacementText, style = {} } = replacement;
    
    // Get the position and size of the original text element
    const rect = element.getBoundingClientRect();
    const layerRect = replacementLayer.getBoundingClientRect();
    
    // Calculate relative position
    const left = rect.left - layerRect.left;
    const top = rect.top - layerRect.top;
    const width = rect.width;
    const height = rect.height;

    // Create overlay element to cover original text
    const overlay = document.createElement('div');
    overlay.className = 'text-replacement text-replacement-overlay';
    overlay.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      background-color: ${style.backgroundColor || '#ffffff'};
      z-index: 1;
      pointer-events: none;
    `;

    // Create replacement text element
    const replacementElement = document.createElement('div');
    replacementElement.className = 'text-replacement text-replacement-text';
    replacementElement.textContent = replacementText;
    
    // Copy font styles from original element
    const computedStyle = window.getComputedStyle(element);
    replacementElement.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      font-family: ${computedStyle.fontFamily};
      font-size: ${computedStyle.fontSize};
      font-weight: ${computedStyle.fontWeight};
      color: ${style.color ||   '#000000'};
      background-color: ${style.backgroundColor || '#fff'};
      border: ${style.border || '1px solid #fff'};
      border-radius: 2px;
      padding: 0;
      margin: 0;
      line-height: ${computedStyle.lineHeight};
      text-align: ${computedStyle.textAlign};
      z-index: 2;
      pointer-events: none;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: ${computedStyle.textAlign === 'center' ? 'center' : 'flex-start'};
    `;

    // Apply custom styles
    if (style.fontSize) replacementElement.style.fontSize = style.fontSize;
    if (style.fontWeight) replacementElement.style.fontWeight = style.fontWeight;
    if (style.fontFamily) replacementElement.style.fontFamily = style.fontFamily;

    // Add elements to replacement layer
    replacementLayer.appendChild(overlay);
    replacementLayer.appendChild(replacementElement);

    // Add data attributes for identification
    overlay.setAttribute('data-page', replacement.page);
    overlay.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-page', replacement.page);
    replacementElement.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-replacement-text', replacementText);
  }

  /**
   * Update replacements when page is transformed (zoom, rotate, etc.)
   */
  updateReplacements(pageNumber) {
    if (!this.hasReplacementsForPage(pageNumber)) {
      return;
    }

    const pageView = this.#pageViews.get(pageNumber);
    if (!pageView) {
      return;
    }

    // Clear existing replacement elements first
    this.#clearReplacementsForPage(pageNumber);

    // Re-render replacements with updated coordinates
    const replacements = this.getReplacementsForPage(pageNumber);
    this.#createReplacementElements(pageView, replacements);
  }

  /**
   * Clear replacement elements for a specific page
   */
  #clearReplacementsForPage(pageNumber) {
    const pageView = this.#pageViews.get(pageNumber);
    if (!pageView || !pageView.div) {
      return;
    }

    // Remove all replacement elements from this page
    const replacementElements = pageView.div.querySelectorAll('.text-replacement');
    replacementElements.forEach(element => {
      element.remove();
    });
  }

  /**
   * Add new replacements programmatically
   */
  addReplacements(newReplacements) {
    if (!Array.isArray(newReplacements)) {
      return;
    }

    // Save state before adding (unless in batch mode)
    if (!this.#batchMode) {
      this.#saveToHistory();
    }

    const addedReplacements = [];

    for (const replacement of newReplacements) {
      if (this.#isValidReplacement(replacement)) {
        const pageNum = replacement.page;
        if (!this.#replacements.has(pageNum)) {
          this.#replacements.set(pageNum, []);
        }
        this.#replacements.get(pageNum).push(replacement);
        addedReplacements.push(replacement);

        // Track batch operation
        if (this.#batchMode) {
          this.#batchOperations.push({
            type: 'add',
            replacement: replacement
          });
        }

        // Render if page is already loaded
        if (this.#pageViews.has(pageNum)) {
          this.#renderReplacementsForPage(pageNum);
        }
      }
    }

    this.#enabled = this.#replacements.size > 0;

    // Emit event if not in batch mode
    if (!this.#batchMode && addedReplacements.length > 0) {
      this.#emitEvent('textReplacementsAdded', {
        replacements: addedReplacements
      });
    }
  }

  /**
   * Clear all replacements
   */
  clearReplacements() {
    // Save state before clearing
    if (!this.#batchMode) {
      this.#saveToHistory();
    }

    // Remove all replacement elements from DOM
    for (const [pageNumber] of this.#pageViews) {
      const pageView = this.#pageViews.get(pageNumber);
      if (pageView) {
        const container = pageView.div;
        const existingReplacements = container.querySelectorAll('.text-replacement');
        existingReplacements.forEach(el => el.remove());
      }
    }

    this.#replacements.clear();
    this.#enabled = false;

    // Emit event
    this.#emitEvent('textReplacementsCleared');
  }

  /**
   * Set replacements (replaces all existing ones)
   */
  setReplacements(newReplacements) {
    if (!Array.isArray(newReplacements)) {
      return;
    }

    // Save state before setting
    if (!this.#batchMode) {
      this.#saveToHistory();
    }

    // Clear existing replacements
    this.clearReplacements();

    // Add new replacements
    this.addReplacements(newReplacements);

    // Emit event
    this.#emitEvent('textReplacementsSet', { replacements: newReplacements });
  }

  /**
   * Add a single replacement
   */
  addSingleReplacement(replacement) {
    if (!this.#isValidReplacement(replacement)) {
      return false;
    }

    // Save state before adding
    if (!this.#batchMode) {
      this.#saveToHistory();
    }

    const pageNum = replacement.page;
    if (!this.#replacements.has(pageNum)) {
      this.#replacements.set(pageNum, []);
    }

    this.#replacements.get(pageNum).push(replacement);
    this.#enabled = true;

    // Render if page is already loaded
    if (this.#pageViews.has(pageNum)) {
      this.#renderReplacementsForPage(pageNum);
    }

    // Emit event
    this.#emitEvent('textReplacementAdded', { replacement });

    return true;
  }

  /**
   * Remove a specific replacement
   */
  removeReplacement(pageNumber, originalText) {
    if (!this.#replacements.has(pageNumber)) {
      return false;
    }

    // Save state before removing
    if (!this.#batchMode) {
      this.#saveToHistory();
    }

    const replacements = this.#replacements.get(pageNumber);
    const index = replacements.findIndex(r => r.originalText === originalText);

    if (index !== -1) {
      const removed = replacements.splice(index, 1)[0];

      // Remove from DOM
      const pageView = this.#pageViews.get(pageNumber);
      if (pageView) {
        const container = pageView.div;
        const elements = container.querySelectorAll(
          `.text-replacement[data-original-text="${originalText}"]`
        );
        elements.forEach(el => el.remove());
      }

      // Clean up empty page entries
      if (replacements.length === 0) {
        this.#replacements.delete(pageNumber);
      }

      this.#enabled = this.#replacements.size > 0;

      // Emit event
      this.#emitEvent('textReplacementRemoved', { replacement: removed });

      return true;
    }

    return false;
  }

  /**
   * Undo the last operation
   */
  undo() {
    if (!this.canUndo()) {
      return false;
    }

    this.#historyIndex--;
    this.#restoreFromHistoryIndex();

    // Emit event
    this.#emitEvent('textReplacementUndone');

    return true;
  }

  /**
   * Redo the next operation
   */
  redo() {
    if (!this.canRedo()) {
      return false;
    }

    this.#historyIndex++;
    this.#restoreFromHistoryIndex();

    // Emit event
    this.#emitEvent('textReplacementRedone');

    return true;
  }

  /**
   * Check if undo is possible
   */
  canUndo() {
    return this.#historyIndex > 0;
  }

  /**
   * Check if redo is possible
   */
  canRedo() {
    return this.#historyIndex < this.#history.length - 1;
  }

  /**
   * Start batch mode (multiple operations without individual history saves)
   */
  startBatch() {
    this.#batchMode = true;
    this.#batchOperations = [];
  }

  /**
   * End batch mode and save to history
   */
  endBatch() {
    if (this.#batchMode) {
      this.#batchMode = false;
      if (this.#batchOperations.length > 0) {
        this.#saveToHistory();
        // Emit batch event
        this.#emitEvent('textReplacementBatchCompleted', {
          operations: this.#batchOperations
        });
      }
      this.#batchOperations = [];
    }
  }

  /**
   * Get current state for external access
   */
  getState() {
    return {
      replacements: Object.fromEntries(this.#replacements),
      enabled: this.#enabled,
      canUndo: this.canUndo(),
      canRedo: this.canRedo(),
      historySize: this.#history.length,
      historyIndex: this.#historyIndex
    };
  }

  /**
   * Create a replacement element for a single text element
   */
  #createSingleElementReplacement(textElement, replacement, replacementLayer) {
    const { elements, targetText } = textElement;
    const element = elements[0]; // Single element
    const { replacementText, style = {} } = replacement;

    // Get precise text bounds with scale awareness
    const bounds = this.#getPreciseTextBounds(element, targetText, replacementLayer);
    const computedStyle = window.getComputedStyle(element);
    const fontSize = parseFloat(computedStyle.fontSize) || 12;

    // Calculate position with adaptive padding for better coverage
    const basePadding = 2;
    const fontPadding = Math.max(1, fontSize * 0.1); // 10% of font size
    const padding = (basePadding + fontPadding) / (bounds.scale || 1); // Adjust padding for scale

    const left = bounds.left - padding;
    const top = bounds.top - padding;
    const width = bounds.width + (padding * 2);
    const height = bounds.height + (padding * 2);

    this.#createReplacementElementsAtPosition(left, top, width, height, element, replacement, replacementLayer, targetText);
  }

  /**
   * Create a replacement element for cross-element text
   */
  #createCrossElementReplacement(textElement, replacement, replacementLayer) {
    const { elements, targetText, involvedElements } = textElement;
    const { replacementText, style = {} } = replacement;

    if (!involvedElements || involvedElements.length === 0) {
      console.warn('TextReplacementManager: No involved elements for cross-element replacement');
      return;
    }

    // Calculate bounding box for all involved elements with scale awareness
    let minLeft = Infinity, minTop = Infinity, maxRight = -Infinity, maxBottom = -Infinity;
    let firstElement = null;
    let maxFontSize = 0;
    let scale = 1;

    // Get scale from the first element
    if (involvedElements.length > 0) {
      const pageView = replacementLayer.closest('.page');
      if (pageView) {
        const transform = pageView.style.transform;
        const scaleMatch = transform.match(/scale\(([^)]+)\)/);
        if (scaleMatch) {
          scale = parseFloat(scaleMatch[1]) || 1;
        }
      }
    }

    const layerRect = replacementLayer.getBoundingClientRect();

    for (const mapping of involvedElements) {
      const bounds = this.#convertToRelativePosition(
        mapping.element.getBoundingClientRect(),
        replacementLayer
      );
      const computedStyle = window.getComputedStyle(mapping.element);
      const fontSize = parseFloat(computedStyle.fontSize) || 12;

      const left = bounds.left;
      const top = bounds.top;
      const right = left + bounds.width;
      const bottom = top + bounds.height;

      if (left < minLeft) minLeft = left;
      if (top < minTop) minTop = top;
      if (right > maxRight) maxRight = right;
      if (bottom > maxBottom) maxBottom = bottom;

      if (fontSize > maxFontSize) maxFontSize = fontSize;
      if (!firstElement) firstElement = mapping.element;
    }

    // Add adaptive padding based on font size and scale
    const basePadding = 2;
    const fontPadding = Math.max(1, maxFontSize * 0.1); // 10% of font size
    const padding = (basePadding + fontPadding) / scale; // Adjust for scale

    minLeft -= padding;
    minTop -= padding;
    maxRight += padding;
    maxBottom += padding;

    const width = maxRight - minLeft;
    const height = maxBottom - minTop;

    this.#createReplacementElementsAtPosition(minLeft, minTop, width, height, firstElement, replacement, replacementLayer, targetText);
  }

  /**
   * Get precise text bounds using Range API with scale awareness
   */
  #getPreciseTextBounds(element, targetText, replacementLayer) {
    try {
      const textContent = element.textContent || '';
      const targetIndex = textContent.indexOf(targetText);

      if (targetIndex === -1) {
        // Fallback to element bounds
        return this.#getElementRelativeBounds(element, replacementLayer);
      }

      // Create range for the target text
      const range = document.createRange();
      const textNode = element.firstChild;

      if (textNode && textNode.nodeType === Node.TEXT_NODE) {
        range.setStart(textNode, targetIndex);
        range.setEnd(textNode, targetIndex + targetText.length);

        const rangeRect = range.getBoundingClientRect();
        if (rangeRect.width > 0 && rangeRect.height > 0) {
          return this.#convertToRelativePosition(rangeRect, replacementLayer);
        }
      }
    } catch (error) {
      console.warn('TextReplacementManager: Error getting precise text bounds:', error);
    }

    // Fallback to element bounds
    return this.#getElementRelativeBounds(element, replacementLayer);
  }

  /**
   * Get element bounds relative to replacement layer
   */
  #getElementRelativeBounds(element, replacementLayer) {
    const rect = element.getBoundingClientRect();
    return this.#convertToRelativePosition(rect, replacementLayer);
  }

  /**
   * Convert absolute position to position relative to replacement layer
   */
  #convertToRelativePosition(rect, replacementLayer) {
    const layerRect = replacementLayer.getBoundingClientRect();

    // Get the page view container to calculate scale
    const pageView = replacementLayer.closest('.page');
    let scale = 1;

    if (pageView) {
      const transform = pageView.style.transform;
      const scaleMatch = transform.match(/scale\(([^)]+)\)/);
      if (scaleMatch) {
        scale = parseFloat(scaleMatch[1]) || 1;
      }
    }

    return {
      left: (rect.left - layerRect.left) / scale,
      top: (rect.top - layerRect.top) / scale,
      width: rect.width / scale,
      height: rect.height / scale,
      scale: scale
    };
  }

  /**
   * Create the actual replacement DOM elements at specified position
   */
  #createReplacementElementsAtPosition(left, top, width, height, referenceElement, replacement, replacementLayer, targetText) {
    const { replacementText, style = {} } = replacement;

    // Create overlay element to cover original text
    const overlay = document.createElement('div');
    overlay.className = 'text-replacement text-replacement-overlay';
    overlay.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      background-color: ${style.overlayColor || '#ffffff'};
      z-index: 1;
      pointer-events: none;
      box-sizing: border-box;
      border-radius: 1px;
    `;

    // Create replacement text element
    const replacementElement = document.createElement('div');
    replacementElement.className = 'text-replacement text-replacement-text';
    replacementElement.textContent = replacementText;

    // Copy font styles from reference element
    const computedStyle = window.getComputedStyle(referenceElement);
    replacementElement.style.cssText = `
      position: absolute;
      left: ${left}px;
      top: ${top}px;
      width: ${width}px;
      height: ${height}px;
      font-family: ${computedStyle.fontFamily};
      font-size: ${computedStyle.fontSize};
      font-weight: ${computedStyle.fontWeight};
      color: ${style.color || '#000000'};
      background-color: ${style.backgroundColor || 'rgba(255, 255, 0, 0.3)'};
      border: ${style.border || '1px solid rgba(255, 255, 0, 0.8)'};
      border-radius: 2px;
      padding: 1px 2px;
      margin: 0;
      line-height: ${computedStyle.lineHeight};
      text-align: ${computedStyle.textAlign};
      z-index: 2;
      pointer-events: none;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: ${computedStyle.textAlign === 'center' ? 'center' : 'flex-start'};
      word-wrap: break-word;
      overflow-wrap: break-word;
    `;

    // Apply custom styles
    if (style.fontSize) replacementElement.style.fontSize = style.fontSize;
    if (style.fontWeight) replacementElement.style.fontWeight = style.fontWeight;
    if (style.fontFamily) replacementElement.style.fontFamily = style.fontFamily;

    // Add elements to replacement layer
    replacementLayer.appendChild(overlay);
    replacementLayer.appendChild(replacementElement);

    // Add data attributes for identification
    overlay.setAttribute('data-page', replacement.page);
    overlay.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-page', replacement.page);
    replacementElement.setAttribute('data-original-text', targetText);
    replacementElement.setAttribute('data-replacement-text', replacementText);
  }

  /**
   * Setup message listener for dynamic updates
   */
  #setupMessageListener() {
    if (typeof window !== 'undefined') {
      window.addEventListener('message', (event) => {
        this.#handleMessage(event);
      });
    }
  }

  /**
   * Handle postMessage events
   */
  #handleMessage(event) {
    try {
      if (!event.data || typeof event.data !== 'object') {
        this.#sendErrorResponse('Invalid message format', event.data);
        return;
      }

      const { type, data, requestId } = event.data;

      switch (type) {
        case 'setTextReplacements':
          this.#handleSetTextReplacements(data, requestId);
          break;
        case 'addTextReplacement':
          this.#handleAddTextReplacement(data, requestId);
          break;
        case 'clearTextReplacements':
          this.#handleClearTextReplacements(requestId);
          break;
        case 'undoTextReplacement':
          this.#handleUndo(requestId);
          break;
        case 'redoTextReplacement':
          this.#handleRedo(requestId);
          break;
        case 'getTextReplacementState':
          this.#handleGetState(requestId);
          break;
        case 'startBatch':
          this.#handleStartBatch(requestId);
          break;
        case 'endBatch':
          this.#handleEndBatch(requestId);
          break;
        case 'removeTextReplacement':
          this.#handleRemoveTextReplacement(data, requestId);
          break;
        default:
          this.#sendErrorResponse(`Unknown message type: ${type}`, { type }, requestId);
      }
    } catch (error) {
      this.#sendErrorResponse('Error processing message', error, event.data?.requestId);
    }
  }

  /**
   * Handle set text replacements request
   */
  #handleSetTextReplacements(data, requestId) {
    try {
      if (!data || !Array.isArray(data.replacements)) {
        this.#sendErrorResponse('Invalid replacements data', data, requestId);
        return;
      }

      const validReplacements = data.replacements.filter(r => this.#isValidReplacement(r));
      if (validReplacements.length !== data.replacements.length) {
        this.#sendWarningResponse(
          `${data.replacements.length - validReplacements.length} invalid replacements filtered out`,
          { total: data.replacements.length, valid: validReplacements.length },
          requestId
        );
      }

      this.setReplacements(validReplacements);
      this.#sendSuccessResponse('Text replacements set successfully', {
        count: validReplacements.length,
        state: this.getState()
      }, requestId);
    } catch (error) {
      this.#sendErrorResponse('Failed to set text replacements', error, requestId);
    }
  }

  /**
   * Handle add text replacement request
   */
  #handleAddTextReplacement(data, requestId) {
    try {
      if (!data || !data.replacement) {
        this.#sendErrorResponse('Invalid replacement data', data, requestId);
        return;
      }

      if (!this.#isValidReplacement(data.replacement)) {
        this.#sendErrorResponse('Invalid replacement format', data.replacement, requestId);
        return;
      }

      const success = this.addSingleReplacement(data.replacement);
      if (success) {
        this.#sendSuccessResponse('Text replacement added successfully', {
          replacement: data.replacement,
          state: this.getState()
        }, requestId);
      } else {
        this.#sendErrorResponse('Failed to add text replacement', data.replacement, requestId);
      }
    } catch (error) {
      this.#sendErrorResponse('Error adding text replacement', error, requestId);
    }
  }

  /**
   * Handle clear text replacements request
   */
  #handleClearTextReplacements(requestId) {
    try {
      const previousCount = this.#getTotalReplacementCount();
      this.clearReplacements();
      this.#sendSuccessResponse('Text replacements cleared successfully', {
        previousCount,
        state: this.getState()
      }, requestId);
    } catch (error) {
      this.#sendErrorResponse('Failed to clear text replacements', error, requestId);
    }
  }

  /**
   * Handle undo request
   */
  #handleUndo(requestId) {
    try {
      if (!this.canUndo()) {
        this.#sendWarningResponse('No operations to undo', { canUndo: false }, requestId);
        return;
      }

      const success = this.undo();
      if (success) {
        this.#sendSuccessResponse('Undo successful', { state: this.getState() }, requestId);
      } else {
        this.#sendErrorResponse('Undo failed', {}, requestId);
      }
    } catch (error) {
      this.#sendErrorResponse('Error during undo', error, requestId);
    }
  }

  /**
   * Handle redo request
   */
  #handleRedo(requestId) {
    try {
      if (!this.canRedo()) {
        this.#sendWarningResponse('No operations to redo', { canRedo: false }, requestId);
        return;
      }

      const success = this.redo();
      if (success) {
        this.#sendSuccessResponse('Redo successful', { state: this.getState() }, requestId);
      } else {
        this.#sendErrorResponse('Redo failed', {}, requestId);
      }
    } catch (error) {
      this.#sendErrorResponse('Error during redo', error, requestId);
    }
  }

  /**
   * Handle get state request
   */
  #handleGetState(requestId) {
    try {
      const state = this.getState();
      this.#sendSuccessResponse('State retrieved successfully', { state }, requestId);
    } catch (error) {
      this.#sendErrorResponse('Failed to get state', error, requestId);
    }
  }

  /**
   * Handle start batch request
   */
  #handleStartBatch(requestId) {
    try {
      this.startBatch();
      this.#sendSuccessResponse('Batch mode started', { batchMode: true }, requestId);
    } catch (error) {
      this.#sendErrorResponse('Failed to start batch mode', error, requestId);
    }
  }

  /**
   * Handle end batch request
   */
  #handleEndBatch(requestId) {
    try {
      this.endBatch();
      this.#sendSuccessResponse('Batch mode ended', {
        batchMode: false,
        state: this.getState()
      }, requestId);
    } catch (error) {
      this.#sendErrorResponse('Failed to end batch mode', error, requestId);
    }
  }

  /**
   * Handle remove text replacement request
   */
  #handleRemoveTextReplacement(data, requestId) {
    try {
      if (!data || !data.pageNumber || !data.originalText) {
        this.#sendErrorResponse('Invalid remove request data', data, requestId);
        return;
      }

      const success = this.removeReplacement(data.pageNumber, data.originalText);
      if (success) {
        this.#sendSuccessResponse('Text replacement removed successfully', {
          pageNumber: data.pageNumber,
          originalText: data.originalText,
          state: this.getState()
        }, requestId);
      } else {
        this.#sendWarningResponse('Text replacement not found', data, requestId);
      }
    } catch (error) {
      this.#sendErrorResponse('Error removing text replacement', error, requestId);
    }
  }

  /**
   * Save current state to history for undo/redo
   */
  #saveToHistory() {
    // Create a deep copy of current state
    const state = {
      replacements: new Map(),
      timestamp: Date.now()
    };

    for (const [pageNum, replacements] of this.#replacements) {
      state.replacements.set(pageNum, [...replacements]);
    }

    // Remove any future history if we're not at the end
    if (this.#historyIndex < this.#history.length - 1) {
      this.#history = this.#history.slice(0, this.#historyIndex + 1);
    }

    // Add new state
    this.#history.push(state);
    this.#historyIndex++;

    // Limit history size
    if (this.#history.length > this.#maxHistorySize) {
      this.#history.shift();
      this.#historyIndex--;
    }

    // Save to session storage
    this.#saveToSessionStorage();
  }

  /**
   * Restore state from session storage
   */
  #restoreFromSessionStorage() {
    try {
      const stored = sessionStorage.getItem(this.#sessionStorageKey);
      if (stored) {
        const data = JSON.parse(stored);
        if (data.replacements && data.history) {
          // Restore replacements
          this.#replacements.clear();
          for (const [pageNum, replacements] of Object.entries(data.replacements)) {
            if (Array.isArray(replacements)) {
              this.#replacements.set(parseInt(pageNum), replacements);
            }
          }

          // Restore history
          this.#history = data.history || [];
          this.#historyIndex = data.historyIndex || -1;

          this.#enabled = this.#replacements.size > 0;

          console.log('TextReplacementManager: Restored from session storage');
          return true;
        }
      }
    } catch (error) {
      console.warn('TextReplacementManager: Failed to restore from session storage:', error);
    }
    return false;
  }

  /**
   * Save current state to session storage
   */
  #saveToSessionStorage() {
    try {
      const data = {
        replacements: Object.fromEntries(this.#replacements),
        history: this.#history,
        historyIndex: this.#historyIndex,
        timestamp: Date.now()
      };
      sessionStorage.setItem(this.#sessionStorageKey, JSON.stringify(data));
    } catch (error) {
      console.warn('TextReplacementManager: Failed to save to session storage:', error);
    }
  }

  /**
   * Restore state from history at current index
   */
  #restoreFromHistoryIndex() {
    if (this.#historyIndex >= 0 && this.#historyIndex < this.#history.length) {
      const state = this.#history[this.#historyIndex];

      // Clear current DOM elements
      for (const [pageNumber] of this.#pageViews) {
        const pageView = this.#pageViews.get(pageNumber);
        if (pageView) {
          const container = pageView.div;
          const existingReplacements = container.querySelectorAll('.text-replacement');
          existingReplacements.forEach(el => el.remove());
        }
      }

      // Restore replacements from history
      this.#replacements.clear();
      for (const [pageNum, replacements] of state.replacements) {
        this.#replacements.set(pageNum, [...replacements]);
      }

      this.#enabled = this.#replacements.size > 0;

      // Re-render all pages
      for (const [pageNumber] of this.#pageViews) {
        if (this.hasReplacementsForPage(pageNumber)) {
          this.#renderReplacementsForPage(pageNumber);
        }
      }

      // Save to session storage
      this.#saveToSessionStorage();
    }
  }

  /**
   * Emit event through event bus or window
   */
  #emitEvent(eventType, data = {}) {
    const eventData = {
      type: eventType,
      source: 'TextReplacementManager',
      timestamp: Date.now(),
      ...data
    };

    // Emit through event bus if available
    if (this.#eventBus && typeof this.#eventBus.dispatch === 'function') {
      this.#eventBus.dispatch(eventType, eventData);
    }

    // Also emit through window for iframe communication
    if (typeof window !== 'undefined' && window.parent !== window) {
      window.parent.postMessage({
        type: 'textReplacementStatus',
        success: true,
        message: eventType,
        data: eventData
      }, '*');
    }

    // Log for debugging
    console.log(`TextReplacementManager: ${eventType}`, eventData);
  }

  /**
   * Setup event bus listeners for responsive updates
   */
  #setupEventBusListeners() {
    if (!this.#eventBus) {
      return;
    }

    // Listen for scale changes
    this.#eventBus._on('scalechanging', (evt) => {
      // Debounce updates during scaling
      if (this.#scaleUpdateTimeout) {
        clearTimeout(this.#scaleUpdateTimeout);
      }
      this.#scaleUpdateTimeout = setTimeout(() => {
        this.#updateAllReplacements();
        this.#scaleUpdateTimeout = null;
      }, 100);
    });

    // Listen for rotation changes
    this.#eventBus._on('rotationchanging', (evt) => {
      // Update all replacements after rotation
      setTimeout(() => {
        this.#updateAllReplacements();
      }, 50);
    });

    // Listen for page rendering events
    this.#eventBus._on('pagerendered', (evt) => {
      if (evt.pageNumber && this.hasReplacementsForPage(evt.pageNumber)) {
        // Small delay to ensure DOM is ready
        setTimeout(() => {
          this.updateReplacements(evt.pageNumber);
        }, 10);
      }
    });

    // Listen for text layer rendering
    this.#eventBus._on('textlayerrendered', (evt) => {
      if (evt.pageNumber && this.hasReplacementsForPage(evt.pageNumber)) {
        // Text layer is ready, update replacements
        setTimeout(() => {
          this.updateReplacements(evt.pageNumber);
        }, 10);
      }
    });

    // Listen for resize events
    if (typeof window !== 'undefined') {
      const resizeHandler = () => {
        if (this.#resizeTimeout) {
          clearTimeout(this.#resizeTimeout);
        }
        this.#resizeTimeout = setTimeout(() => {
          this.#updateAllReplacements();
          this.#resizeTimeout = null;
        }, 200);
      };

      window.addEventListener('resize', resizeHandler);

      // Store reference for cleanup
      this.#resizeHandler = resizeHandler;
    }
  }

  /**
   * Update all replacements across all pages
   */
  #updateAllReplacements() {
    for (const [pageNumber] of this.#pageViews) {
      if (this.hasReplacementsForPage(pageNumber)) {
        this.updateReplacements(pageNumber);
      }
    }
  }

  /**
   * Cleanup method for removing event listeners
   */
  destroy() {
    // Clear timeouts
    if (this.#scaleUpdateTimeout) {
      clearTimeout(this.#scaleUpdateTimeout);
    }
    if (this.#resizeTimeout) {
      clearTimeout(this.#resizeTimeout);
    }

    // Remove resize listener
    if (this.#resizeHandler && typeof window !== 'undefined') {
      window.removeEventListener('resize', this.#resizeHandler);
    }

    // Clear all replacements
    this.clearReplacements();

    // Clear references
    this.#pageViews.clear();
    this.#replacements.clear();
    this.#history = [];
    this.#historyIndex = -1;
  }

  /**
   * Get total replacement count across all pages
   */
  #getTotalReplacementCount() {
    let count = 0;
    for (const [, replacements] of this.#replacements) {
      count += replacements.length;
    }
    return count;
  }

  /**
   * Send success response
   */
  #sendSuccessResponse(message, data = {}, requestId = null) {
    this.#sendResponse('success', message, data, requestId);
  }

  /**
   * Send error response
   */
  #sendErrorResponse(message, error = {}, requestId = null) {
    console.error('TextReplacementManager Error:', message, error);
    this.#sendResponse('error', message, { error }, requestId);
  }

  /**
   * Send warning response
   */
  #sendWarningResponse(message, data = {}, requestId = null) {
    console.warn('TextReplacementManager Warning:', message, data);
    this.#sendResponse('warning', message, data, requestId);
  }

  /**
   * Send response message
   */
  #sendResponse(type, message, data = {}, requestId = null) {
    const response = {
      type: 'textReplacementResponse',
      status: type,
      message,
      data,
      timestamp: Date.now(),
      requestId
    };

    // Emit through event bus if available
    if (this.#eventBus && typeof this.#eventBus.dispatch === 'function') {
      this.#eventBus.dispatch('textReplacementResponse', response);
    }

    // Also emit through window for iframe communication
    if (typeof window !== 'undefined' && window.parent !== window) {
      window.parent.postMessage(response, '*');
    }

    // Log for debugging
    console.log(`TextReplacementManager Response [${type}]:`, message, data);
  }

  /**
   * Validate API request
   */
  #validateRequest(request, requiredFields = []) {
    if (!request || typeof request !== 'object') {
      return { valid: false, error: 'Invalid request format' };
    }

    for (const field of requiredFields) {
      if (!(field in request)) {
        return { valid: false, error: `Missing required field: ${field}` };
      }
    }

    return { valid: true };
  }

  /**
   * Enhanced validation for replacement objects
   */
  #isValidReplacement(replacement) {
    if (!replacement || typeof replacement !== 'object') {
      return false;
    }

    // Required fields
    if (!replacement.page || typeof replacement.page !== 'number' || replacement.page < 1) {
      return false;
    }

    if (!replacement.originalText || typeof replacement.originalText !== 'string' || replacement.originalText.trim() === '') {
      return false;
    }

    if (!replacement.replacementText || typeof replacement.replacementText !== 'string') {
      return false;
    }

    // Optional fields validation
    if (replacement.style && typeof replacement.style !== 'object') {
      return false;
    }

    if (replacement.position && (
      typeof replacement.position !== 'object' ||
      typeof replacement.position.x !== 'number' ||
      typeof replacement.position.y !== 'number'
    )) {
      return false;
    }

    return true;
  }
}

export { TextReplacementManager };
