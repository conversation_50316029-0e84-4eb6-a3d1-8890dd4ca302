<template>
  <div class="pdf-reader-example">
    <div class="example-header">
      <h1>PDF文本替换组件示例</h1>
      <p>基于PDF.js实现的文本替换预览功能</p>
    </div>

    <div class="example-controls">
      <div class="control-section">
        <h3>快速测试</h3>
        <div class="button-group">
          <button @click="loadBasicExample" class="btn btn-primary">
            基础示例
          </button>
          <button @click="loadChineseExample" class="btn btn-success">
            中文替换
          </button>
          <button @click="loadStyleExample" class="btn btn-warning">
            样式测试
          </button>
          <button @click="clearAll" class="btn btn-danger">
            清除所有
          </button>
        </div>
      </div>

      <div class="control-section">
        <h3>动态配置</h3>
        <div class="config-form">
          <div class="form-row">
            <label>PDF文件:</label>
            <select v-model="selectedPdf" @change="changePdf">
              <option value="compressed.tracemonkey-pldi-09.pdf">TraceMonkey论文</option>
              <option value="sample.pdf">示例文档</option>
              <option value="custom">自定义URL</option>
            </select>
          </div>
          
          <div class="form-row" v-if="selectedPdf === 'custom'">
            <label>自定义PDF URL:</label>
            <input v-model="customPdfUrl" type="text" placeholder="输入PDF文件URL" />
          </div>
        </div>
      </div>
    </div>

    <!-- PDF阅读器组件 -->
    <PdfReader
      ref="pdfReader"
      :pdf-url="currentPdfUrl"
      :initial-replacements="initialReplacements"
      :show-controls="true"
      :viewer-height="'700px'"
      :viewer-path="viewerPath"
      @pdf-loaded="onPdfLoaded"
      @replacements-applied="onReplacementsApplied"
      @replacement-added="onReplacementAdded"
      @replacement-removed="onReplacementRemoved"
      @replacements-cleared="onReplacementsCleared"
    />

    <!-- 事件日志 -->
    <div class="event-log" v-if="eventLogs.length > 0">
      <h3>事件日志</h3>
      <div class="log-container">
        <div 
          v-for="(log, index) in eventLogs" 
          :key="index"
          :class="['log-item', log.type]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="btn btn-secondary btn-sm">清除日志</button>
    </div>
  </div>
</template>

<script>
import PdfReader from './PdfReader.vue'

export default {
  name: 'PdfReaderExample',
  components: {
    PdfReader
  },
  
  data() {
    return {
      selectedPdf: 'compressed.tracemonkey-pldi-09.pdf',
      customPdfUrl: '',
      viewerPath: 'build/generic/web/viewer.html',
      initialReplacements: [],
      eventLogs: []
    }
  },
  
  computed: {
    currentPdfUrl() {
      return this.selectedPdf === 'custom' ? this.customPdfUrl : this.selectedPdf;
    }
  },
  
  methods: {
    // 加载基础示例
    loadBasicExample() {
      const replacements = [
        {
          page: 1,
          originalText: "JavaScript",
          replacementText: "TypeScript",
          colorTheme: 'yellow'
        },
        {
          page: 1,
          originalText: "Mozilla",
          replacementText: "OpenAI",
          colorTheme: 'green'
        }
      ];
      
      this.setReplacements(replacements);
      this.addLog('加载基础示例：JavaScript → TypeScript, Mozilla → OpenAI', 'info');
    },
    
    // 加载中文替换示例
    loadChineseExample() {
      const replacements = [
        {
          page: 1,
          originalText: "trace",
          replacementText: "追踪",
          colorTheme: 'pink'
        },
        {
          page: 1,
          originalText: "monkey",
          replacementText: "猴子",
          colorTheme: 'blue'
        },
        {
          page: 1,
          originalText: "compilation",
          replacementText: "编译",
          colorTheme: 'purple'
        }
      ];
      
      this.setReplacements(replacements);
      this.addLog('加载中文替换示例', 'info');
    },
    
    // 加载样式测试示例
    loadStyleExample() {
      const replacements = [
        {
          page: 1,
          originalText: "performance",
          replacementText: "性能表现",
          colorTheme: 'orange'
        },
        {
          page: 1,
          originalText: "optimization",
          replacementText: "优化策略",
          colorTheme: 'red'
        }
      ];
      
      this.setReplacements(replacements);
      this.addLog('加载样式测试示例', 'info');
    },
    
    // 清除所有替换
    clearAll() {
      this.$refs.pdfReader.clearReplacements();
      this.addLog('清除所有文本替换', 'warning');
    },
    
    // 切换PDF文件
    changePdf() {
      if (this.selectedPdf !== 'custom') {
        this.$refs.pdfReader.setPdfUrl(this.selectedPdf);
        this.addLog(`切换PDF文件: ${this.selectedPdf}`, 'info');
      }
    },
    
    // 设置替换配置
    setReplacements(replacements) {
      this.$refs.pdfReader.setReplacements(replacements);
    },
    
    // 事件处理器
    onPdfLoaded(data) {
      this.addLog(`PDF加载完成: ${data.pdfUrl}`, 'success');
    },
    
    onReplacementsApplied(replacements) {
      this.addLog(`应用了 ${replacements.length} 个文本替换`, 'success');
    },
    
    onReplacementAdded(replacement) {
      this.addLog(`添加文本替换: "${replacement.originalText}" → "${replacement.replacementText}"`, 'success');
    },
    
    onReplacementRemoved(replacement) {
      this.addLog(`移除文本替换: "${replacement.originalText}"`, 'warning');
    },
    
    onReplacementsCleared() {
      this.addLog('清除了所有文本替换', 'warning');
    },
    
    // 添加日志
    addLog(message, type = 'info') {
      const now = new Date();
      const time = now.toLocaleTimeString();
      
      this.eventLogs.unshift({
        time,
        message,
        type
      });
      
      // 限制日志数量
      if (this.eventLogs.length > 50) {
        this.eventLogs = this.eventLogs.slice(0, 50);
      }
    },
    
    // 清除日志
    clearLogs() {
      this.eventLogs = [];
    }
  }
}
</script>

<style scoped>
.pdf-reader-example {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.example-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.example-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 28px;
}

.example-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.example-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.control-section {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.control-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-success { background-color: #28a745; color: white; }
.btn-warning { background-color: #ffc107; color: #212529; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-sm { padding: 6px 12px; font-size: 12px; }

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-row label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-row select,
.form-row input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.event-log {
  margin-top: 30px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.event-log h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 15px;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f8f9fa;
  display: flex;
  gap: 10px;
  font-size: 13px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.info { background-color: #f8f9fa; }
.log-item.success { background-color: #d4edda; }
.log-item.warning { background-color: #fff3cd; }
.log-item.error { background-color: #f8d7da; }

.log-time {
  color: #6c757d;
  font-weight: 500;
  min-width: 80px;
}

.log-message {
  flex: 1;
  color: #333;
}

@media (max-width: 768px) {
  .example-controls {
    grid-template-columns: 1fr;
  }
  
  .button-group {
    justify-content: center;
  }
}
</style>
